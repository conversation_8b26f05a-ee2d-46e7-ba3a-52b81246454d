from django.http import HttpResponseRedirect
from drf_spectacular.utils import extend_schema, OpenApiParameter
from rest_framework import generics, permissions, status
from rest_framework.response import Response
from rest_framework.views import APIView

from core.exceptions import raise_serializer_error_msg
from core.utils import validate_payment
from .models import Bank
from . import serializers
from .serializers import FundWalletSerializerIn


class GetBankListView(generics.ListAPIView):
    queryset = Bank.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.GetBankListSerializer


class FundWalletAPIView(APIView):
    @extend_schema(request=FundWalletSerializerIn, responses={status.HTTP_200_OK})
    def post(self, request):
        serializer = FundWalletSerializerIn(data=request.data, context={"request": request})
        serializer.is_valid() or raise_serializer_error_msg(errors=serializer.errors)
        response = serializer.save()
        return Response({"detail": response})


class PaymentVerifyAPIView(APIView):
    permission_classes = []

    @extend_schema(parameters=[OpenApiParameter(name="reference", type=str)])
    def get(self, request):
        reference = request.GET.get("reference")
        success, return_url = validate_payment(reference)
        return HttpResponseRedirect(redirect_to=return_url)

    


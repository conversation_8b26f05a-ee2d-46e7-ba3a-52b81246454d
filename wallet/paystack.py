import hmac
import hashlib
import requests
from requests.auth import HTTPBasicAuth
from urllib.parse import quote
from uuid import uuid4, UUID
from datetime import datetime
from decimal import Decimal

from django.conf import settings


class PaystackClient:
    _secret_key = settings.PAYSTACK_SECRET_KEY
    base_url = settings.PAYSTACK_BASE_URL

    ACCESS_TOKEN = {'key': None}

    urls = {
        "get_transaction": f"{base_url}/transaction/verify",
        "create_account": f"{base_url}/dedicated_account/assign",
        "verify_bvn": f"{base_url}/api/v1/vas/bvn-details-match",
        "all_banks": f"{base_url}/bank",
        "transferrecipient": f"{base_url}/transferrecipient",
        "disbursement": f"{base_url}/transfer",
        "authorize-disbursement": f"{base_url}/transfer/finalize_transfer",
        "validate-disbursements-acct": f"{base_url}/bank/resolve",
        "disbursement-summary": f"{base_url}/transfer/verify",

        "create-customer": f"{base_url}/customer",
        "initialize": f"{base_url}/transaction/initialize",
    }

    def _make_http_request(self, method:str, url:str, json:str = None, params=None, return_full_response=False, raise_exception=True):
        with requests.sessions.Session() as session:
            print(method, url, params, {"Authorization": f"Bearer {self.access_token}"}, json)
            response = session.request(method, url, params=params, headers={"Authorization": f"Bearer {self.access_token}"}, json=json).json()
            print(response)
            if raise_exception is False:
                return response
            if response['status'] is False:
                raise Exception(response)
            return response if return_full_response else response['data']
        
    def verify_signature(self, payload, signature):
        computed_signature = hmac.new(
            self._secret_key.encode('utf-8'),
            msg=payload,
            digestmod=hashlib.sha512
        ).hexdigest()
        return hmac.compare_digest(signature, computed_signature)
    
    def request_transaction(self, transaction_reference):
        response = self._make_http_request('get', f'{self.urls["get_transaction"]}/{transaction_reference}')
        return {
            'currency': response['currency'],
            'paymentStatus': 'PAID' if response['status'] == 'success' else 'UNPAID',
            'customer': response['customer'],
            'amountPaid': response['amount'],
            'settlementAmount': response['amount'],
            'transactionReference': response['reference'],
            'paidOn': response['paid_at'],
            'paymentMethod': response['channel'],
        }

    def load_deposit_transaction(self, transactionReference):
        eventData = self.request_transaction(transactionReference)

        if eventData['currency'].upper() == 'NGN' and eventData['paymentStatus'].upper() == 'PAID':
            response_bool = True
        else:
            response_bool = False

        data = { item:eventData[item] for item in ('customer', 'amountPaid', 'settlementAmount', 'transactionReference', 'paidOn', 'paymentStatus', 'paymentMethod')}
        return response_bool, data
    
    @property
    def access_token(self):
        return self._secret_key
    
    def _create_customer(self, email, first_name, last_name, phone_number=None):
        data = {
            "email": email,
            "first_name": first_name,
            "last_name": last_name,
        }
        if phone_number:
            data['phone'] = phone_number
        response = self._make_http_request('post', self.urls['create-customer'], json=data)
        return response

    def _get_create_customer(self, email, first_name, last_name):
        response = self._make_http_request('get', f"{self.urls['create-customer']}/{email}", raise_exception=False)
        if response['status'] is True:
            return response['data']
        return self._create_customer(email, first_name, last_name)

    def generate_bank_account(self, bvn, first_name, middle_name, last_name, email, phone_number):
        body = {
            "email": email,
            "first_name": first_name,
            "middle_name": middle_name,
            "last_name": last_name,
            "phone": phone_number,
            "preferred_bank": "test-bank",#"wema-bank",
            "country": "NG",
            "bvn": bvn,
        }

        if not body['middle_name']:
            body.pop('middle_name')

        if not body['phone']:
            body.pop('phone')

        eventData = self._make_http_request('post', self.urls['create_account'], json=body)
        print(eventData)
        return {
            'account_reference': eventData['accountReference'],
            'account_name': eventData['accountName'],
            'bank_name': eventData['accounts'][0]['bankName'],
            'bank_code': eventData['accounts'][0]['bankCode'],
            'account_number': eventData['accounts'][0]['accountNumber']
        }


    # def verify_identity(self, bvn, name=None, dateOfBirth=None, mobileNo=None):
    #     body = {
    #         'bvn': bvn,
    #         # "name": name,#"Benjamin Ranae RT",
    #         # "dateOfBirth": dateOfBirth,#"03-Oct-1993",
    #         # "mobileNo": mobileNo,#"***********"
    #     }
    #     if dateOfBirth: body['dateOfBirth'] = dateOfBirth
    #     if mobileNo: body['mobileNo'] = mobileNo
    #     if name: body['name'] = name
    #     return self._make_http_request('post', self.urls['verify_bvn'], json=body)


    def fetch_all_banks_info(self):
        return self._make_http_request('get', self.urls['all_banks'], params={"country": "nigeria"})
    
    def _create_transfer_recipient(self, name, account_number, bank_code):
        url = f"{self.base_url}/transferrecipient"
        data = {
            "type": "nuban",
            "name": name,
            "account_number": account_number,
            "bank_code": bank_code,
            "currency": "NGN"
        }
        response = self._make_http_request('post', self.urls['transferrecipient'], json=data)
        return response

    def dusburst_withdraw_funds(self, name, amount, reference, narration, bank_code, account_number):
        recipient_code = self._create_transfer_recipient(name, account_number, bank_code)['recipient_code']
        body = {
            "source": "balance",
            "amount": amount,
            "recipient": recipient_code,
            "reason": narration,
            "reference": reference
        }
        return self._make_http_request('post', self.urls['disbursement'], json=body)
    
    def disburstment_validate_account(self, account_number, bank_code):
        obj = self._make_http_request('get', self.urls['validate-disbursements-acct'], params={'account_number': account_number, 'bank_code': bank_code})
        return {
            "account_number": obj['account_number'],
            "account_name": obj['account_name'],
            "bank_code": bank_code,
        }
    
    def authorize_disburstment(self, transaction_id, otp):
        response = self.disburstment_transaction_summary(transaction_id)
        return self._make_http_request('post', self.urls['authorize-disbursement'], json={'transfer_code': response['transfer_code'], 'otp': otp})

    # def resend_disbursement_otp(self, transaction_id):
    #     return self._make_http_request('post', self.urls['resend-disbursement-otp'], json={'reference': transaction_id})
    
    def disburstment_transaction_summary(self, transaction_id):
        return self._make_http_request('get', f'{self.urls["disbursement-summary"]}/{transaction_id}')

    def transaction_initialize(self, email, amount, callback_url, reference_no):
        data = {
            "email": email,
            "amount": amount * 100,
            "channels": ["bank_transfer"],
            "callback_url": callback_url,
            "reference": reference_no
        }
        response = self._make_http_request('post', self.urls['initialize'], json=data)
        return response


    
    
    
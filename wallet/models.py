from django.db import models
from authentication.models import User
from uuid import uuid4


class TransactionTypeChoices(models.TextChoices):
    FUND_WALLET = ("fund_wallet", "Fund Wallet")
    ORDER_PAYMENT = ("order", "Order Payment")
    WITHDRAWAL = ("withdrawal", "Withdrawal")
    REFUND = ("refund", "Refund")
    BONUS = ("bonus", "Bonus")


class TransactionStatusChoices(models.TextChoices):
    COMPLETED = ("completed", "Completed")
    PENDING = ("pending", "Pending")
    FAILED = ("failed", "Failed")


class Bank(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    name = models.CharField(max_length=150)
    code = models.CharField(max_length=10)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.name

    
class BankInformation(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.OneToOneField(User, related_name='bank_info', on_delete=models.CASCADE)
    bank = models.ForeignKey(Bank, related_name='bank_users', on_delete=models.CASCADE)
    account_number = models.CharField(max_length=10)
    account_name = models.CharField(max_length=150, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.user} {self.bank}"


class UserWallet(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    balance = models.FloatField(default=0)
    customer_id = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id}: {self.user} - {self.balance}"


class Transaction(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, blank=True, null=True)
    transaction_type = models.CharField(max_length=60, choices=TransactionTypeChoices.choices, default="fund_wallet")
    amount = models.FloatField(default=0)
    amount_paid = models.FloatField(default=0)
    status = models.CharField(max_length=50, choices=TransactionStatusChoices.choices, default="pending")
    narration = models.CharField(max_length=200, blank=True, null=True)
    reference = models.CharField(max_length=300, blank=True, null=True)
    return_url = models.URLField(max_length=300, blank=True, null=True)
    provider_response = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username}: amount - {self.amount}"


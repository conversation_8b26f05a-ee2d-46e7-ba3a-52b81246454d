from django.core.management.base import BaseCommand
from django.db import transaction
from wallet.paystack import PaystackClient
from wallet.models import Bank

class Command(BaseCommand):
    help = 'Fetches the supported banks from paystack and updates the local database'

    def handle(self, *args, **kwargs):
        data = PaystackClient().fetch_all_banks_info()

        if data:
            banks = [
                Bank(
                    name=bank['name'], 
                    code=bank["code"],
                    ) for bank in data
                ]
            self.stdout.write(self.style.SUCCESS(f'{len(banks)} banks fetched successfully. Refreshing the database.'))
            with transaction.atomic():
                # Refresh the database
                Bank.objects.all().delete()
                Bank.objects.bulk_create(banks)

            self.stdout.write(self.style.SUCCESS(f'{len(banks)} banks updated successfully!'))
        else:
            self.stdout.write(self.style.ERROR('No supported banks found'))

from rest_framework import serializers

from core.utils import perform_payment_action
from .models import Bank, UserWallet
from .paystack import PaystackClient

client = PaystackClient()


class GetBankListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Bank
        fields = ['name', 'code']


class UserWalletSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = UserWallet
        exclude = ["user", "customer_id"]


class FundWalletSerializerIn(serializers.Serializer):
    amount = serializers.FloatField()
    return_url = serializers.URLField()

    def create(self, validated_data):
        amount = validated_data.get("amount")
        return_url = validated_data.get("return_url")

        request = self.context.get("request")
        payment_link = perform_payment_action(request, amount, return_url, "fund_wallet", "Wallet funding")

        return payment_link




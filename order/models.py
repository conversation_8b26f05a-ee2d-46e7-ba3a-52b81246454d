from uuid import uuid4

from django.db import models
from authentication.models import User
from foods.models import Food
from rider.models import DispatchDriver


class FoodTrayStatusChoices(models.TextChoices):
    OPEN = ("open", "Open")
    CLOSED = ("closed", "Closed")


class OrderStatusChoices(models.TextChoices):
    DELIVERED = ("delivered", "Delivered")
    PROCESSING = ("processing", "Processing")
    CANCELLED = ("cancelled", "Cancelled")
    READY_FOR_PICKUP = ("ready_for_pickup", "Ready For Pickup")
    WITH_DISPATCH_RIDER = ("with_dispatch_rider", "With Dispatch Rider")
    NEW = ("new", "New")


class PaymentModeChoices(models.TextChoices):
    WALLET = ("wallet", "Wallet")
    BANK_TRANSFER = ("bank_transfer", "Bank Transfer")


class OrderTrackingStatusChoices(models.TextChoices):
    CONFIRMED = ("confirmed", "Confirmed")
    PACKING = ("packed", "Packed")
    EN_ROUTE = ("en_route", "EN ROUTE")
    DELIVERED = ("delivered", "Delivered")


class SupportChatStatusChoices(models.TextChoices):
    OPEN = ("open", "Open")
    CLOSED = ("closed", "Closed")
    RESOLVED = ("resolved", "Resolved")


class FoodTray(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    status = models.CharField(max_length=50, choices=FoodTrayStatusChoices.choices, default="open")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id}: {self.user}-{self.status}"


class FoodTrayItem(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    food_tray = models.ForeignKey(FoodTray, on_delete=models.CASCADE)
    food = models.ForeignKey(Food, on_delete=models.CASCADE)
    price = models.FloatField(default=1.0)
    quantity = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return "{}: {} {}".format(self.id, self.food_tray, self.food)


class DeliveryAddress(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    address = models.CharField(max_length=200)
    is_default = models.BooleanField(default=False)
    state = models.CharField(max_length=100, blank=True, null=True)
    longitude = models.CharField(max_length=100, blank=True, null=True)
    latitude = models.CharField(max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user}: {self.address}"

    class Meta:
        verbose_name_plural = "Delivery Addresses"


class CheckoutBill(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    food_tray = models.OneToOneField(FoodTray, on_delete=models.CASCADE)
    item_total = models.FloatField(default=1.0)
    discount = models.FloatField(default=0.0)
    delivery_fee = models.FloatField(default=1.0)
    service_fee = models.FloatField(default=1.0)
    total = models.FloatField(default=0.0)
    paid_at = models.DateTimeField(blank=True, null=True)
    payment_reference = models.CharField(max_length=300, blank=True, null=True)
    meta_data = models.TextField(blank=True, null=True)
    payment_mode = models.CharField(max_length=50, choices=PaymentModeChoices.choices, default="wallet")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.food_tray} - {self.total}"


class Order(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    unique_id = models.CharField(max_length=200, blank=True, null=True, unique=True, editable=False)
    payment = models.ForeignKey(CheckoutBill, on_delete=models.SET_NULL, blank=True, null=True)
    address = models.ForeignKey(DeliveryAddress, on_delete=models.SET_NULL, blank=True, null=True)
    status = models.CharField(max_length=50, choices=OrderStatusChoices.choices, default="new")
    prep_instruction = models.TextField(blank=True, null=True)
    contact_phone = models.CharField(max_length=30)
    delivered_at = models.DateTimeField(blank=True, null=True)
    cancelled_at = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id}: {self.user}"


class OrderTracking(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    order = models.ForeignKey(Order, on_delete=models.CASCADE)
    tracking_id = models.CharField(max_length=200, blank=True, null=True)
    rider = models.ForeignKey(DispatchDriver, on_delete=models.SET_NULL, blank=True, null=True)
    status = models.CharField(max_length=50, choices=OrderTrackingStatusChoices.choices, default="confirmed")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id} - {self.order.user.email}"


class SupportChatSession(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_chat_sessions')
    subject = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=20, choices=SupportChatStatusChoices.choices, default=SupportChatStatusChoices.OPEN)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    closed_at = models.DateTimeField(blank=True, null=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Support Chat {self.id} - {self.user} - {self.status}"


class SupportChatMessage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    chat_session = models.ForeignKey(SupportChatSession, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_messages')
    message = models.TextField()
    is_from_support = models.BooleanField(default=False)  # True if message is from support team
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['created_at']

    def __str__(self):
        sender_type = "Support" if self.is_from_support else "User"
        return f"{sender_type} message in {self.chat_session.id}"


class FoodReview(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='food_reviews')
    food = models.ForeignKey(Food, on_delete=models.CASCADE, related_name='order_reviews')
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='food_reviews')
    rating = models.PositiveSmallIntegerField(
        choices=[(i, i) for i in range(1, 6)],  # 1 to 5 rating
        help_text="Rating from 1 to 5"
    )
    review_text = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'food', 'order')  # One review per user per food per order
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user} - {self.food} - {self.rating}/5"

    def clean(self):    
        from django.core.exceptions import ValidationError
        # Ensure the order is delivered before allowing review
        if self.order.status != OrderStatusChoices.DELIVERED:
            raise ValidationError("Can only review food from delivered orders.")

        # Ensure the food was part of this order
        if self.order.payment and self.order.payment.food_tray:
            if not self.order.payment.food_tray.items.filter(food=self.food).exists():
                raise ValidationError("Can only review food that was part of this order.")







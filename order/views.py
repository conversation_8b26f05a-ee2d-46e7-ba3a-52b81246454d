from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db import transaction
from .models import FoodTray, FoodTrayItem, DeliveryAddress, Order, OrderTracking, SupportChatSession, SupportChatMessage, FoodReview
from .serializers import (
    FoodTraySerializer, FoodTrayItemSerializer, AddToCartSerializer,
    UpdateCartItemSerializer, DeliveryAddressSerializer, CheckoutSerializer,
    PaymentSerializer, OrderSerializer, OrderTrackingSerializer,
    SupportChatSessionSerializer, SupportChatMessageSerializer, CreateSupportChatMessageSerializer,
    FoodReviewSerializer, CreateFoodReviewSerializer
)


class CartView(generics.RetrieveAPIView):
    serializer_class = FoodTraySerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        food_tray, created = FoodTray.objects.get_or_create(
            user=self.request.user,
            status='open',
            defaults={'user': self.request.user}
        )
        return food_tray


class AddToCartView(generics.CreateAPIView):
    serializer_class = AddToCartSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        food_tray_item = serializer.save()

        food_tray = food_tray_item.food_tray
        cart_serializer = FoodTraySerializer(food_tray)

        return Response({
            'message': 'Item added to cart successfully',
            'cart': cart_serializer.data
        }, status=status.HTTP_201_CREATED)


class UpdateCartItemView(generics.UpdateAPIView):
    # Update quantity of item in cart or remove if quantity is 0
    serializer_class = UpdateCartItemSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        item_id = self.kwargs['item_id']
        return get_object_or_404(
            FoodTrayItem,
            id=item_id,
            food_tray__user=self.request.user,
            food_tray__status='open'
        )

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data)
        serializer.is_valid(raise_exception=True)

        updated_item = serializer.save()

        if updated_item is None:
            message = 'Item removed from cart successfully'
        else:
            message = 'Item quantity updated successfully'

        # Return the updated cart
        food_tray = instance.food_tray
        cart_serializer = FoodTraySerializer(food_tray)

        return Response({
            'message': message,
            'cart': cart_serializer.data
        })


class RemoveFromCartView(generics.DestroyAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        item_id = self.kwargs['item_id']
        return get_object_or_404(
            FoodTrayItem,
            id=item_id,
            food_tray__user=self.request.user,
            food_tray__status='open'
        )

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        food_tray = instance.food_tray
        instance.delete()

        cart_serializer = FoodTraySerializer(food_tray)

        return Response({
            'message': 'Item removed from cart successfully',
            'cart': cart_serializer.data
        })


class ClearCartView(generics.GenericAPIView):
    permission_classes = [permissions.IsAuthenticated]

    def delete(self, request):
        try:
            food_tray = FoodTray.objects.get(user=request.user, status='open')
            food_tray.foodtrayitem_set.all().delete()

            cart_serializer = FoodTraySerializer(food_tray)
            return Response({
                'message': 'Cart cleared successfully',
                'cart': cart_serializer.data
            })
        except FoodTray.DoesNotExist:
            return Response({
                'message': 'No active cart found'
            }, status=status.HTTP_404_NOT_FOUND)


class DeliveryAddressListCreateView(generics.ListCreateAPIView):
    serializer_class = DeliveryAddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return DeliveryAddress.objects.filter(user=self.request.user).order_by('-created_at')


class DeliveryAddressDetailView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = DeliveryAddressSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return DeliveryAddress.objects.filter(user=self.request.user)


class CheckoutView(generics.CreateAPIView):
    serializer_class = CheckoutSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        result = serializer.save()
        checkout_bill = result['checkout_bill']

        return Response({
            'message': 'Checkout completed successfully',
            'total_amount': checkout_bill.total,
            'payment_required': True
        }, status=status.HTTP_201_CREATED)


class PaymentView(generics.CreateAPIView):
    serializer_class = PaymentSerializer
    permission_classes = [permissions.IsAuthenticated]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        result = serializer.save()

        return Response({
            'message': 'Payment processed successfully',
            'order_id': result['order'].id,
            'tracking_id': result['tracking_id'],
            'payment_status': result['payment_status'],
            'amount_paid': result['amount_paid']
        }, status=status.HTTP_200_OK)


# Order Management Views
class OrderListView(generics.ListAPIView):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user).order_by('-created_at')


class OrderDetailView(generics.RetrieveAPIView):
    serializer_class = OrderSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return Order.objects.filter(user=self.request.user)


class OrderTrackingView(generics.RetrieveAPIView):
    serializer_class = OrderTrackingSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'tracking_id'

    def get_queryset(self):
        return OrderTracking.objects.filter(order__user=self.request.user)


class OrderTrackingByOrderView(generics.RetrieveAPIView):
    serializer_class = OrderTrackingSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        order_id = self.kwargs['order_id']
        return get_object_or_404(
            OrderTracking,
            order__id=order_id,
            order__user=self.request.user
        )


# Support Chat Views
class SupportChatSessionListCreateView(generics.ListCreateAPIView):
    serializer_class = SupportChatSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return SupportChatSession.objects.filter(user=self.request.user)


class SupportChatSessionDetailView(generics.RetrieveUpdateAPIView):
    serializer_class = SupportChatSessionSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return SupportChatSession.objects.filter(user=self.request.user)


class SupportChatMessageCreateView(generics.CreateAPIView):
    serializer_class = CreateSupportChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_context(self):
        context = super().get_serializer_context()
        chat_session_id = self.kwargs['chat_session_id']
        chat_session = get_object_or_404(
            SupportChatSession,
            id=chat_session_id,
            user=self.request.user
        )
        context['chat_session'] = chat_session
        return context

    def perform_create(self, serializer):
        # Update the chat session's updated_at timestamp
        chat_session = self.get_serializer_context()['chat_session']
        chat_session.save()  # This will update the updated_at field
        serializer.save()


class SupportChatMessageListView(generics.ListAPIView):
    serializer_class = SupportChatMessageSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        chat_session_id = self.kwargs['chat_session_id']
        # Ensure the chat session belongs to the current user
        chat_session = get_object_or_404(
            SupportChatSession,
            id=chat_session_id,
            user=self.request.user
        )
        return SupportChatMessage.objects.filter(chat_session=chat_session)


# Food Review Views
class FoodReviewCreateView(generics.CreateAPIView):
    serializer_class = CreateFoodReviewSerializer
    permission_classes = [permissions.IsAuthenticated]


class FoodReviewListView(generics.ListAPIView):
    serializer_class = FoodReviewSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        food_id = self.request.query_params.get('food_id')
        if not food_id:
            return Response(
                {"error": "food_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        return FoodReview.objects.filter(food_id=food_id)

    def list(self, request, *args, **kwargs):
        food_id = request.query_params.get('food_id')
        if not food_id:
            return Response(
                {"error": "food_id query parameter is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)



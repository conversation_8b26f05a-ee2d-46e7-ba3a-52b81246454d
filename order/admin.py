from django.contrib import admin
from .models import (
    FoodTray, FoodTrayItem, DeliveryAddress, CheckoutBill,
    Order, OrderTracking, SupportChatSession, SupportChatMessage, FoodReview
)


@admin.register(FoodTray)
class FoodTrayAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'status', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__email', 'user__first_name']


@admin.register(FoodTrayItem)
class FoodTrayItemAdmin(admin.ModelAdmin):
    list_display = ['id', 'food_tray', 'food', 'quantity', 'price']
    search_fields = ['food__name', 'food_tray__user__email']


@admin.register(DeliveryAddress)
class DeliveryAddressAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'address', 'is_default']
    list_filter = ['is_default']
    search_fields = ['user__email', 'address']


@admin.register(CheckoutBill)
class CheckoutBillAdmin(admin.ModelAdmin):
    list_display = ['id', 'total', 'paid_at', 'created_at']
    list_filter = ['paid_at', 'created_at']
    search_fields = ['user__email']


@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'status', 'contact_phone', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__email', 'contact_phone']


@admin.register(OrderTracking)
class OrderTrackingAdmin(admin.ModelAdmin):
    list_display = ['id', 'order', 'tracking_id', 'status', 'created_at']
    list_filter = ['status', 'created_at']
    search_fields = ['tracking_id', 'order__user__email']


@admin.register(SupportChatSession)
class SupportChatSessionAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'subject', 'status', 'created_at', 'closed_at']
    list_filter = ['status', 'created_at']
    search_fields = ['user__email', 'subject']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(SupportChatMessage)
class SupportChatMessageAdmin(admin.ModelAdmin):
    list_display = ['id', 'chat_session', 'sender', 'is_from_support', 'created_at']
    list_filter = ['is_from_support', 'created_at']
    search_fields = ['sender__email', 'message']
    readonly_fields = ['created_at']


@admin.register(FoodReview)
class FoodReviewAdmin(admin.ModelAdmin):
    list_display = ['id', 'user', 'food', 'order', 'rating', 'created_at']
    list_filter = ['rating', 'created_at']
    search_fields = ['user__email', 'food__name', 'review_text']
    readonly_fields = ['created_at', 'updated_at']

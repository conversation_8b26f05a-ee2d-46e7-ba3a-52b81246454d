import json

from rest_framework import serializers
from django.db import transaction
from django.utils import timezone
from authentication.models import User, MetaData
from core.utils import generate_transaction_reference, perform_payment_action
from foods.models import Food
from wallet.models import UserWallet, Transaction
from wallet.paystack import PaystackClient
from .models import (
    FoodTray, FoodTrayItem, DeliveryAddress, CheckoutBill,
    Order, OrderTracking, PaymentModeChoices, OrderTrackingStatusChoices,
    SupportChatSession, SupportChatMessage, FoodReview
)


class FoodTrayItemSerializer(serializers.ModelSerializer):
    food_name = serializers.CharField(source='food.name', read_only=True)
    food_price = serializers.DecimalField(source='food.price', max_digits=14, decimal_places=2, read_only=True)
    food_image = serializers.ImageField(source='food.display_image', read_only=True)
    total_price = serializers.SerializerMethodField()

    class Meta:
        model = FoodTrayItem
        fields = ['id', 'food', 'food_name', 'food_price', 'food_image', 'price', 'quantity', 'total_price', 'created_at']
        read_only_fields = ['id', 'created_at']

    def get_total_price(self, obj):
        return obj.price * obj.quantity


class FoodTraySerializer(serializers.ModelSerializer):
    items = FoodTrayItemSerializer(source='foodtrayitem_set', many=True, read_only=True)
    total_items = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()

    class Meta:
        model = FoodTray
        fields = ['id', 'status', 'items', 'total_items', 'total_amount', 'created_at', 'updated_at']
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']

    def get_total_items(self, obj):
        return obj.foodtrayitem_set.count()

    def get_total_amount(self, obj):
        return sum(item.price * item.quantity for item in obj.foodtrayitem_set.all())


class AddToCartSerializer(serializers.Serializer):
    food_id = serializers.UUIDField()
    quantity = serializers.IntegerField(min_value=1, default=1)

    def validate_food_id(self, value):
        try:
            food = Food.objects.get(id=value, displayed=True)
            return food
        except Food.DoesNotExist:
            raise serializers.ValidationError("Food item not found or not available.")

    def create(self, validated_data):
        user = self.context['request'].user
        food = validated_data['food_id']
        quantity = validated_data['quantity']

        # Get or create open food tray for user
        food_tray, created = FoodTray.objects.get_or_create(
            user=user, 
            status='open',
            defaults={'user': user}
        )

        # Check if item already exists in cart
        food_tray_item, item_created = FoodTrayItem.objects.get_or_create(
            food_tray=food_tray,
            food=food,
            defaults={
                'price': float(food.price),
                'quantity': quantity
            }
        )

        if not item_created:
            # Update quantity if item already exists
            food_tray_item.quantity += quantity
            food_tray_item.save()

        return food_tray_item


class UpdateCartItemSerializer(serializers.Serializer):
    quantity = serializers.IntegerField(min_value=0)

    def update(self, instance, validated_data):
        quantity = validated_data['quantity']
        
        if quantity == 0:
            instance.delete()
            return None
        else:
            instance.quantity = quantity
            instance.save()
            return instance


class DeliveryAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryAddress
        fields = ['id', 'address', 'state', 'longitude', 'latitude', 'created_at']
        read_only_fields = ['id', 'user', 'created_at']

    def create(self, validated_data):
        user = self.context['request'].user
        return DeliveryAddress.objects.create(user=user, **validated_data)


class CheckoutBillSerializer(serializers.ModelSerializer):
    class Meta:
        model = CheckoutBill
        fields = [
            'id', 'item_total', 'discount', 'delivery_fee', 
            'service_fee', 'total', 'payment_mode', 'created_at'
        ]
        read_only_fields = ['id', 'food_tray', 'paid_at', 'created_at']


class OrderSerializer(serializers.ModelSerializer):
    payment_details = CheckoutBillSerializer(source='payment', read_only=True)
    delivery_address = DeliveryAddressSerializer(source='address', read_only=True)

    class Meta:
        model = Order
        fields = [
            'id', 'status', 'prep_instruction', 'contact_phone',
            'payment_details', 'delivery_address', 'delivered_at', 
            'cancelled_at', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'payment', 'address', 'delivered_at', 'cancelled_at', 'created_at', 'updated_at']


class OrderTrackingSerializer(serializers.ModelSerializer):
    order_details = OrderSerializer(source='order', read_only=True)

    class Meta:
        model = OrderTracking
        fields = ['id', 'tracking_id', 'status', 'order_details', 'created_at', 'updated_at']
        read_only_fields = ['id', 'order', 'tracking_id', 'rider', 'created_at', 'updated_at']


class CheckoutSerializer(serializers.Serializer):
    payment_mode = serializers.ChoiceField(choices=PaymentModeChoices.choices, default='wallet')

    def validate(self, attrs):
        user = self.context['request'].user
        
        # Check if user has an open food tray with items
        try:
            food_tray = FoodTray.objects.get(user=user, status='open')
            if not food_tray.foodtrayitem_set.exists():
                raise serializers.ValidationError("Cart is empty.")
        except FoodTray.DoesNotExist:
            raise serializers.ValidationError("No active cart found.")
            
        attrs['food_tray'] = food_tray
        return attrs

    def create(self, validated_data):
        food_tray = validated_data['food_tray']
        payment_mode = validated_data['payment_mode']

        with transaction.atomic():
            # Calculate totals
            items = food_tray.foodtrayitem_set.all()
            item_total = sum(item.price * item.quantity for item in items)
            delivery_fee = 500.0  # Fixed delivery fee (This should be retrieved from a ConstantTable)
            service_fee = item_total * 0.05  # 5% service fee (This should be retrieved from a ConstantTable)
            discount = 0.0
            total = item_total + delivery_fee + service_fee - discount

            # Create checkout bill
            checkout_bill = CheckoutBill.objects.create(
                food_tray=food_tray,
                item_total=item_total,
                discount=discount,
                delivery_fee=delivery_fee,
                service_fee=service_fee,
                total=total,
                payment_mode=payment_mode
            )

            # Close the food tray
            food_tray.status = 'closed'
            food_tray.save()

            return {
                'checkout_bill': checkout_bill,
            }


class PaymentSerializer(serializers.Serializer):
    checkout_bill_id = serializers.UUIDField()
    delivery_address_id = serializers.UUIDField()
    return_url = serializers.URLField(required=False)
    prep_instruction = serializers.CharField(max_length=500, required=False, allow_blank=True)
    contact_phone = serializers.CharField(max_length=30)

    def validate_checkout_bill_id(self, value):
        user = self.context['request'].user
        try:
            checkout_bill = CheckoutBill.objects.get(id=value, user=user)
            if checkout_bill.paid_at:
                raise serializers.ValidationError("Order has already been paid.")
            return checkout_bill
        except CheckoutBill.DoesNotExist:
            raise serializers.ValidationError("CheckoutBill not found.")

    def validate_delivery_address_id(self, value):
        user = self.context['request'].user
        try:
            address = DeliveryAddress.objects.get(id=value, user=user)
            return address
        except DeliveryAddress.DoesNotExist:
            raise serializers.ValidationError("Delivery address not found.")

    def create(self, validated_data):
        checkout_bill = validated_data['checkout_bill_id']
        user = self.context['request'].user
        delivery_address = validated_data['delivery_address_id']
        prep_instruction = validated_data.get('prep_instruction', '')
        return_url = validated_data.get('return_url')
        contact_phone = validated_data['contact_phone']

        amount = checkout_bill.total
        description = ""

        with transaction.atomic():
            # May need to change this implementation later.
            # Assumption here is that wallet_balance can be found in MetaData
            user_wallet, _ = UserWallet.objects.get_or_create(user=user)
            reference_number = generate_transaction_reference()
            order_meta = json.dumps({
                "delivery_address_id": delivery_address.id,
                "instruction": prep_instruction,
                "contact_phone": contact_phone
            })
            checkout_bill.payment_reference = reference_number
            checkout_bill.meta_data = order_meta
            checkout_bill.save()
            if checkout_bill.payment_mode == 'wallet':
                trans = Transaction.objects.create(
                    user=user, transaction_type="order", amount=amount, amount_paid=amount, reference=reference_number
                )
                # Handle wallet payment
                current_balance = user_wallet.balance

                if current_balance < checkout_bill.total:
                    raise serializers.ValidationError("Insufficient wallet balance.")
                
                # Deduct from wallet
                new_balance = current_balance - amount
                user_wallet -= new_balance
                user_wallet.save()

                # Mark as paid
                checkout_bill.paid_at = timezone.now()
                checkout_bill.save()

                # Create order
                order = Order.objects.create(
                    user=user, payment=checkout_bill, address=delivery_address, prep_instruction=prep_instruction, contact_phone=contact_phone
                )

                # Create order tracking
                tracking = OrderTracking.objects.create(order=order, tracking_id=f"TRK-{order.id.hex[:8].upper()}", status='confirmed')

                description = f"Payment for Order {tracking.tracking_id}"
                trans.narration = description
                trans.save()

                return {
                    'order': order,
                    'payment_status': 'paid',
                    'amount_paid': checkout_bill.total,
                    'tracking_id': tracking.tracking_id,
                    'payment_url': None
                }

            if checkout_bill.payment_mode == 'bank_transfer':
                # Handle bank transfer via payment gateway
                try:
                    request = self.context.get("request")
                    if not return_url:
                        raise serializers.ValidationError(f"Return url is required for bank transfers")

                    payment_url = perform_payment_action(request, amount, return_url, "order", "")
                    return {
                        'order': None,
                        'payment_status': 'pending',
                        'amount_paid': 0,
                        'tracking_id': None,
                        'payment_url': payment_url
                    }
                except Exception as e:
                    raise serializers.ValidationError(f"Payment gateway error: {str(e)}")


class SupportChatMessageSerializer(serializers.ModelSerializer):
    sender_name = serializers.CharField(source='sender.first_name', read_only=True)
    sender_email = serializers.CharField(source='sender.email', read_only=True)

    class Meta:
        model = SupportChatMessage
        fields = [
            'id', 'message', 'is_from_support', 'created_at',
            'sender_name', 'sender_email'
        ]
        read_only_fields = ['id', 'created_at', 'sender_name', 'sender_email']


class SupportChatSessionSerializer(serializers.ModelSerializer):
    messages = SupportChatMessageSerializer(many=True, read_only=True)
    user_name = serializers.CharField(source='user.first_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)

    class Meta:
        model = SupportChatSession
        fields = [
            'id', 'subject', 'status', 'created_at', 'updated_at',
            'closed_at', 'messages', 'user_name', 'user_email'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at', 'closed_at', 'user_name', 'user_email']

    def create(self, validated_data):
        user = self.context['request'].user
        return SupportChatSession.objects.create(user=user, **validated_data)


class CreateSupportChatMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = SupportChatMessage
        fields = ['message']

    def create(self, validated_data):
        user = self.context['request'].user
        chat_session = self.context['chat_session']

        return SupportChatMessage.objects.create(
            chat_session=chat_session,
            sender=user,
            is_from_support=False,  # User messages are never from support
            **validated_data
        )


class FoodReviewSerializer(serializers.ModelSerializer):
    user_name = serializers.CharField(source='user.first_name', read_only=True)
    user_email = serializers.CharField(source='user.email', read_only=True)
    food_name = serializers.CharField(source='food.name', read_only=True)
    food_image = serializers.CharField(source='food.display_image.url', read_only=True)

    class Meta:
        model = FoodReview
        fields = [
            'id', 'rating', 'review_text', 'created_at', 'updated_at',
            'user_name', 'user_email', 'food_name', 'food_image'
        ]
        read_only_fields = ['id', 'user', 'food', 'order', 'created_at', 'updated_at']


class CreateFoodReviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = FoodReview
        fields = ['food', 'order', 'rating', 'review_text']

    def validate(self, data):
        user = self.context['request'].user
        food = data['food']
        order = data['order']

        # Check if order belongs to user
        if order.user != user:
            raise serializers.ValidationError("You can only review your own orders.")

        # Check if order is delivered
        if order.status != 'delivered':
            raise serializers.ValidationError("You can only review food from delivered orders.")

        # Check if food was part of this order
        if not order.payment.food_tray.items.filter(food=food).exists():
            raise serializers.ValidationError("You can only review food that was part of this order.")

        # Check if user already reviewed this food for this order
        if FoodReview.objects.filter(user=user, food=food, order=order).exists():
            raise serializers.ValidationError("You have already reviewed this food for this order.")

        return data

    def create(self, validated_data):
        user = self.context['request'].user
        return FoodReview.objects.create(user=user, **validated_data)


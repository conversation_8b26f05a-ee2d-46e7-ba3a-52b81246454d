from rest_framework import serializers
from django.db import transaction
from authentication.models import User
from .models import ChefSpecialService, ChefUser


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'user_type', 'email', 'phone_number', 'first_name', 'last_name', 'address', 'birth_date', 'date_joined', 'longitude', 'latitude', 'has_completed_kyc')
        read_only_fields = ('id', 'user_type', 'date_joined', 'phone_number', 'email', 'has_completed_kyc')
        extra_kwargs = {
            'first_name': {'allow_null': False},
            'last_name': {'allow_null': False},
            'address': {'allow_null': False},
            'birth_date': {'allow_null': False},
            'longitude': {'allow_null': False},
            'latitude': {'allow_null': False},
        }


class ChefDetailsSerializer(serializers.ModelSerializer):
    special_services = serializers.SerializerMethodField()

    def get_special_services(self, obj:ChefUser)->list[str]:
        return [serivice.name for serivice in obj.special_service.all()]
    
    class Meta:
        model = ChefUser
        fields = ['display_image', 'culnary_experience', 'prof_accreditation', 'prof_certificate', 'years_cooking', 'school_attended', 'special_service', 'special_services', 'other_special_services', 'about']
        extra_kwargs = {
            'special_service': {'write_only': True},
        }


class ChefUserProfileSerializer(serializers.ModelSerializer):
    chef = ChefDetailsSerializer()
    class Meta:
        model = User
        fields = ('id', 'user_type', 'email', 'phone_number', 'first_name', 'last_name', 'address', 'birth_date', 'date_joined', 'longitude', 'latitude', 'has_completed_kyc', 'chef')
        read_only_fields = ('id', 'user_type', 'date_joined', 'phone_number', 'email', 'has_completed_kyc')
        extra_kwargs = {
            'first_name': {'allow_null': False},
            'last_name': {'allow_null': False},
            'address': {'allow_null': False},
            'birth_date': {'allow_null': False},
            'longitude': {'allow_null': False},
            'latitude': {'allow_null': False},
        }

    def update(self, instance, validated_data):
        chef_data = validated_data.pop('chef', {})
        special_service = chef_data.pop('special_service', [])

        chef:ChefUser = instance.chef

        with transaction.atomic():
            for k, v in chef_data.items():
                setattr(chef, k, v)
            if special_service:
                chef.special_service.set(special_service)
            chef.save()
            instance = super().update(instance, validated_data)
        return instance


class GetChefSpecialServiceListSerializer(serializers.ModelSerializer):
    class Meta:
        model = ChefSpecialService
        fields = ['id', 'name']


class ChefPublicDetailsSerializer(serializers.ModelSerializer):
    special_services = serializers.SerializerMethodField()

    def get_special_services(self, obj:ChefUser)->list[str]:
        return [serivice.name for serivice in obj.special_service.all()]
    
    class Meta:
        model = ChefUser
        fields = ['display_image', 'vetted', 'certified', 'years_cooking', 'school_attended', 'special_services', 'about',]
        


class ChefUserPublicProfileSerializer(serializers.ModelSerializer):
    chef = ChefPublicDetailsSerializer()
    class Meta:
        model = User
        fields = ('id', 'first_name', 'last_name', 'chef')


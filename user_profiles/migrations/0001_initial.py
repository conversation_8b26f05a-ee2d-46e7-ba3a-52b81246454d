# Generated by Django 5.1.7 on 2025-05-05 05:13

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ChefSpecialService',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='ChefUser',
            fields=[
                ('id', models.UUIDField(editable=False, primary_key=True, serialize=False)),
                ('display_image', models.ImageField(upload_to='users/chefs/display_image/')),
                ('years_cooking', models.PositiveInteger<PERSON>ield()),
                ('school_attended', models.Char<PERSON>ield(default=None, max_length=255, null=True)),
                ('vetted', models.BooleanField(default=False)),
                ('culnary_experience', models.Char<PERSON>ield(choices=[('cook', 'Cook'), ('baker', 'Baker'), ('home_cook', 'Home Cook'), ('pastry_chef', 'Pastry Chef'), ('restaurant_chef', 'Restaurant Chef'), ('self_taught_chef', 'Self-taught Chef'), ('chef', 'Chef'), ('hostel_chef', 'Hostel Chef'), ('professional_chef', 'Professional Chef')], max_length=50)),
                ('prof_accreditation', models.CharField(choices=[('food_handling', 'Food Handling'), ('safety_license', 'Safety Licence'), ('culnary_school_certificate', 'Culnary School Certificate')], default=None, max_length=50, null=True)),
                ('prof_certificate', models.FileField(default=None, null=True, upload_to='users/chefs/prof_cert/')),
                ('other_special_services', models.CharField(default=None, max_length=255, null=True)),
                ('about', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('special_service', models.ManyToManyField(related_name='chefs', to='user_profiles.chefspecialservice')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='chef', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

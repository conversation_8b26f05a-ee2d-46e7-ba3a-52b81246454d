from rest_framework import generics, viewsets, mixins, exceptions, permissions
from authentication.models import User
from . import serializers
from .models import ChefSpecialService
from authentication.permissions import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>d<PERSON>, IsRider, IsUser


class UserProfileUpdateView(generics.RetrieveUpdateDestroyAPIView):
    queryset = User.objects.filter(is_active=True)
    serializer_class = serializers.UserProfileSerializer
    permission_classes = [IsUser] #TODO: possibly make it customer only
    http_method_names = ['get', 'patch', 'delete']

    def get_object(self):
        return self.request.user
    

class ChefProfileUpdateView(generics.RetrieveUpdateDestroyAPIView):
    queryset = User.objects.filter(is_active=True)
    serializer_class = serializers.ChefUserProfileSerializer
    permission_classes = [IsChef] #TODO: possibly make it customer only
    http_method_names = ['get', 'patch', 'delete']

    def get_object(self):
        return self.request.user
    

class GetChefSpecialServiceListView(generics.ListAPIView):
    queryset = ChefSpecialService.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.GetChefSpecialServiceListSerializer


class ChefPublicProfileView(generics.RetrieveAPIView):
    queryset = User.objects.select_related('chef').prefetch_related('special_service').filter(is_active=True)
    serializer_class = serializers.ChefUserPublicProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_url_kwarg = 'chef_id'
    lookup_field = 'chef__id'






from django.core.management.base import BaseCommand
from django.db import transaction
from user_profiles.models import ChefSpecialService

class Command(BaseCommand):
    help = 'Seeds amenities to the local database'

    def handle(self, *args, **kwargs):
        data = [
            'Culinary Instructor', 'Hotel Chef',
            "Event Catering", 'Consultancy',
            'Home Services', 'Restaurant Chef',
            'Fine Diner Chef', 'Others',
            ]

        objs = [ChefSpecialService(name=name) for name in data]
        with transaction.atomic():
            # Refresh the database
            ChefSpecialService.objects.all().delete()
            ChefSpecialService.objects.bulk_create(objs)

        self.stdout.write(self.style.SUCCESS(f'{len(objs)} assets updated successfully!'))
        

from django.db import models
from rest_framework import exceptions
from authentication.models import User
from uuid import uuid4


class ChefSpecialService(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    name = models.Char<PERSON>ield(max_length=50)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return self.name


class ChefUser(models.Model):
    id = models.UUIDField(primary_key=True, editable=False)
    user = models.OneToOneField(User, related_name='chef', on_delete=models.CASCADE)
    display_image = models.ImageField(upload_to="users/chefs/display_image/")
    years_cooking = models.PositiveIntegerField()
    school_attended = models.CharField(max_length=255, null=True, default=None)
    vetted = models.BooleanField(default=False)
    class CULNARY_EXPERIENCE(models.TextChoices):
        COOK = ('cook', 'Cook')
        BAKER = ('baker', 'Baker')
        HOME_COOK = ('home_cook', "Home Cook")
        PASTRY_CHEF = ('pastry_chef', 'Pastry Chef')
        RESTAURANT_CHEF = ('restaurant_chef', 'Restaurant Chef')
        SELF_TAUGHT_CHEF = ('self_taught_chef', 'Self-taught Chef')
        CHEF = ('chef', 'Chef')
        HOSTEL_CHEF = ('hostel_chef', 'Hostel Chef')
        PROFESSIONAL_CHEF = ('professional_chef', 'Professional Chef')
    culnary_experience = models.CharField(max_length=50, choices=CULNARY_EXPERIENCE.choices)
    class PROF_ACCRED(models.TextChoices):
        FOOD_HANDLING = ('food_handling', 'Food Handling')
        SAFETY_LICENSE = ('safety_license', 'Safety Licence')
        CUL_SCHOOL_CERT = ('culnary_school_certificate', 'Culnary School Certificate')
    prof_accreditation = models.CharField(max_length=50, choices=PROF_ACCRED.choices, null=True, default=None)
    prof_certificate = models.FileField(upload_to='users/chefs/prof_cert/', null=True, default=None)
    certified = models.BooleanField(default=False)
    special_service = models.ManyToManyField(ChefSpecialService, related_name='chefs')
    other_special_services = models.CharField(max_length=255, null=True, default=None)
    about = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"Chef: {self.user}"

    def save(self, *args, **kwargs) -> None:
        if not self.id:
            self.id = self.user.id
        if self.culnary_experience == self.CULNARY_EXPERIENCE.PROFESSIONAL_CHEF:
            if not self.prof_accreditation or not self.prof_certificate:
                raise exceptions.ValidationError("You must set and upload professional certification!")
        return super().save(*args, **kwargs)
    


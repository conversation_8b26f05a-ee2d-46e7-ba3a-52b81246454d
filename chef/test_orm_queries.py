"""
Test script to verify ORM queries work correctly.
Run this with: python manage.py shell < chef/test_orm_queries.py
"""

from django.contrib.auth import get_user_model
from user_profiles.models import ChefUser
from foods.models import Kitchen, Food
from order.models import Order, FoodTray, FoodTrayItem, CheckoutBill, DeliveryAddress
from chef.utils import get_chef_analytics, get_orders_for_chef, get_foods_for_chef

User = get_user_model()

def test_orm_queries():
    print("Testing ORM queries for Chef API...")
    
    # Test 1: Check if we can create basic test data
    try:
        # Create a chef user
        chef_user = User.objects.filter(user_type='chef').first()
        if not chef_user:
            print("No chef user found. Creating test data...")
            chef_user = User.objects.create_user(
                email='<EMAIL>',
                password='testpass123',
                user_type='chef',
                first_name='Test',
                last_name='Chef'
            )
            
            # Create chef profile
            chef_profile = ChefUser.objects.create(
                user=chef_user,
                display_image='test_image.jpg',
                years_cooking=5,
                culnary_experience='chef',
                about='Test chef'
            )
            
            # Create kitchen
            kitchen = Kitchen.objects.create(
                chef=chef_profile,
                cover_image='kitchen.jpg',
                name='Test Kitchen',
                type='home_kitchen',
                available=True
            )
            
            # Create food item
            food = Food.objects.create(
                kitchen=kitchen,
                display_image='food.jpg',
                name='Test Food',
                description='Test food item',
                price=1000.00,
                status='approved',
                lunch=True
            )
            
            print("Test data created successfully!")
        else:
            print(f"Using existing chef user: {chef_user.email}")
            
    except Exception as e:
        print(f"Error creating test data: {e}")
        return False
    
    # Test 2: Test get_chef_from_user utility
    try:
        from chef.utils import get_chef_from_user
        chef_profile = get_chef_from_user(chef_user)
        if chef_profile:
            print(f"✓ get_chef_from_user works: {chef_profile}")
        else:
            print("✗ get_chef_from_user returned None")
            return False
    except Exception as e:
        print(f"✗ get_chef_from_user failed: {e}")
        return False
    
    # Test 3: Test kitchen relationship
    try:
        kitchen = chef_profile.kitchen
        print(f"✓ Kitchen relationship works: {kitchen.name}")
    except Exception as e:
        print(f"✗ Kitchen relationship failed: {e}")
        return False
    
    # Test 4: Test get_foods_for_chef utility
    try:
        foods = get_foods_for_chef(chef_profile)
        print(f"✓ get_foods_for_chef works: Found {foods.count()} foods")
    except Exception as e:
        print(f"✗ get_foods_for_chef failed: {e}")
        return False
    
    # Test 5: Test get_orders_for_chef utility (should work even with no orders)
    try:
        orders = get_orders_for_chef(chef_profile)
        print(f"✓ get_orders_for_chef works: Found {orders.count()} orders")
    except Exception as e:
        print(f"✗ get_orders_for_chef failed: {e}")
        return False
    
    # Test 6: Test analytics utility
    try:
        analytics = get_chef_analytics(chef_profile, 'daily')
        print(f"✓ get_chef_analytics works: {analytics['orders_received_count']} orders received")
    except Exception as e:
        print(f"✗ get_chef_analytics failed: {e}")
        return False
    
    # Test 7: Test the specific ORM query that was failing
    try:
        from django.utils import timezone
        from datetime import timedelta
        
        start_date = timezone.now() - timedelta(days=1)
        chef_kitchen = chef_profile.kitchen
        
        # This is the query that was failing before
        chef_orders = Order.objects.filter(
            payment__food_tray__foodtrayitem_set__food__kitchen=chef_kitchen,
            created_at__gte=start_date
        ).distinct()
        
        print(f"✓ Fixed ORM query works: Found {chef_orders.count()} orders")
    except Exception as e:
        print(f"✗ Fixed ORM query failed: {e}")
        return False
    
    print("\n🎉 All ORM queries are working correctly!")
    return True

if __name__ == "__main__":
    test_orm_queries()

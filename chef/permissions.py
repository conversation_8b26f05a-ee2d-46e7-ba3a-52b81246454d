from rest_framework import permissions
from foods.models import Food
from order.models import Order


class IsChefOwner(permissions.BasePermission):
    """
    Custom permission to only allow chefs to access their own resources.
    """
    
    def has_object_permission(self, request, view, obj):
        # Check if user is a chef
        if not (request.user and request.user.is_authenticated and 
                request.user.user_type == request.user.USER_TYPE.CHEF):
            return False
        
        # Check ownership based on object type
        if isinstance(obj, Order):
            # For orders, check if any food item in the order belongs to the chef's kitchen
            if obj.payment and obj.payment.food_tray:
                food_items = obj.payment.food_tray.items.all()
                for item in food_items:
                    if item.food.kitchen.chef.user == request.user:
                        return True
            return False
        
        elif isinstance(obj, Food):
            # For food items, check if the food belongs to chef's kitchen
            return obj.kitchen.chef.user == request.user
        
        # For other objects, try to get the chef through common patterns
        if hasattr(obj, 'kitchen') and hasattr(obj.kitchen, 'chef'):
            return obj.kitchen.chef.user == request.user
        elif hasattr(obj, 'chef') and hasattr(obj.chef, 'user'):
            return obj.chef.user == request.user
        elif hasattr(obj, 'user'):
            return obj.user == request.user
        
        return False


class CanManageOrder(permissions.BasePermission):
    """
    Permission to check if chef can manage a specific order.
    """
    
    def has_object_permission(self, request, view, obj):
        if not isinstance(obj, Order):
            return False
        
        # Check if user is a chef
        if not (request.user and request.user.is_authenticated and 
                request.user.user_type == request.user.USER_TYPE.CHEF):
            return False
        
        # Check if the order contains food items from this chef's kitchen
        if obj.payment and obj.payment.food_tray:
            food_items = obj.payment.food_tray.items.all()
            for item in food_items:
                if item.food.kitchen.chef.user == request.user:
                    return True
        
        return False

"""
Example test cases for Chef API endpoints.
These are example tests to demonstrate how to test the Chef API functionality.
"""

from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from user_profiles.models import ChefUser
from foods.models import Kitchen, Food
from order.models import Order, FoodTray, FoodTrayItem, CheckoutBill, DeliveryAddress
from chef.models import OrderCancellationReason, KitchenAvailabilitySettings

User = get_user_model()


class ChefAPITestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        # Create a chef user
        self.chef_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type='chef',
            first_name='<PERSON>',
            last_name='Chef'
        )

        # Create chef profile
        self.chef_profile = ChefUser.objects.create(
            user=self.chef_user,
            display_image='test_image.jpg',
            years_cooking=5,
            culnary_experience='chef',
            about='Experienced chef'
        )

        # Create kitchen
        self.kitchen = Kitchen.objects.create(
            chef=self.chef_profile,
            cover_image='kitchen.jpg',
            name='Test Kitchen',
            type='home_kitchen',
            available=True
        )

        # Create food items
        self.food1 = Food.objects.create(
            kitchen=self.kitchen,
            display_image='food1.jpg',
            name='Jollof Rice',
            description='Delicious jollof rice',
            price=1000.00,
            status='approved',
            lunch=True,
            dinner=True
        )

        # Create a customer user
        self.customer_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type='user',
            first_name='Jane',
            last_name='Customer'
        )

        # Create API client
        self.client = APIClient()

    def authenticate_chef(self):
        """Authenticate as chef"""
        self.client.force_authenticate(user=self.chef_user)

    def authenticate_customer(self):
        """Authenticate as customer"""
        self.client.force_authenticate(user=self.customer_user)

    def create_test_order(self):
        """Create a test order"""
        # Create food tray
        food_tray = FoodTray.objects.create(
            user=self.customer_user,
            status='closed'
        )

        # Add items to tray
        FoodTrayItem.objects.create(
            food_tray=food_tray,
            food=self.food1,
            quantity=2,
            price=1000.00
        )

        # Create checkout bill
        checkout_bill = CheckoutBill.objects.create(
            food_tray=food_tray,
            item_total=2000.00,
            delivery_fee=500.00,
            service_fee=100.00,
            total=2600.00,
            payment_mode='wallet'
        )

        # Create delivery address
        address = DeliveryAddress.objects.create(
            user=self.customer_user,
            address='123 Test Street',
            state='Lagos',
            latitude='12.3456',
            longitude='12.3456',
            is_default=True
        )

        # Create order
        order = Order.objects.create(
            user=self.customer_user,
            payment=checkout_bill,
            address=address,
            status='new',
            contact_phone='+1234567890'
        )

        return order


class ChefOrderListTest(ChefAPITestCase):
    def test_list_chef_orders(self):
        """Test listing chef's orders"""
        self.authenticate_chef()

        # Create test order
        order = self.create_test_order()

        url = reverse('chef-orders-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertIn('status_counts', response.data)
        self.assertEqual(len(response.data['results']), 1)

    def test_filter_orders_by_status(self):
        """Test filtering orders by status"""
        self.authenticate_chef()

        # Create test orders with different statuses
        order1 = self.create_test_order()
        order2 = self.create_test_order()
        order2.status = 'processing'
        order2.save()

        url = reverse('chef-orders-list')
        response = self.client.get(url, {'status': 'new'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['status'], 'new')


class ChefOrderDetailTest(ChefAPITestCase):
    def test_get_order_detail(self):
        """Test getting order detail"""
        self.authenticate_chef()

        order = self.create_test_order()

        url = reverse('chef-order-detail', kwargs={'pk': order.id})
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['id'], str(order.id))
        self.assertIn('customer', response.data)
        self.assertIn('payment_details', response.data)


class ChefFoodListTest(ChefAPITestCase):
    def test_list_chef_foods(self):
        """Test listing chef's food items"""
        self.authenticate_chef()

        url = reverse('chef-foods-list')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['name'], 'Jollof Rice')


class ChefOrderStatusUpdateTest(ChefAPITestCase):
    def test_update_order_status(self):
        """Test updating order status"""
        self.authenticate_chef()

        order = self.create_test_order()

        url = reverse('chef-order-status-update', kwargs={'pk': order.id})
        data = {'status': 'processing'}
        response = self.client.patch(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)

        # Verify order status was updated
        order.refresh_from_db()
        self.assertEqual(order.status, 'processing')


class ChefOrderCancelTest(ChefAPITestCase):
    def test_cancel_order(self):
        """Test cancelling an order"""
        self.authenticate_chef()

        order = self.create_test_order()

        url = reverse('chef-order-cancel', kwargs={'order_id': order.id})
        data = {
            'reason': 'ingredients_not_available',
            'custom_reason': ''
        }
        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)

        # Verify order was cancelled
        order.refresh_from_db()
        self.assertEqual(order.status, 'cancelled')
        self.assertTrue(hasattr(order, 'cancellation_reason'))


class ChefAnalyticsTest(ChefAPITestCase):
    def test_analytics_dashboard(self):
        """Test analytics dashboard"""
        self.authenticate_chef()

        # Create some test data
        order = self.create_test_order()
        order.status = 'delivered'
        order.save()

        url = reverse('chef-analytics-dashboard')
        response = self.client.get(url, {'period': 'daily'})

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('analytics', response.data)
        self.assertIn('orders_received_count', response.data['analytics'])


class ChefKitchenAvailabilityTest(ChefAPITestCase):
    def test_get_kitchen_availability(self):
        """Test getting kitchen availability settings"""
        self.authenticate_chef()

        url = reverse('chef-kitchen-availability')
        response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('availability_type', response.data)

    def test_update_kitchen_availability(self):
        """Test updating kitchen availability settings"""
        self.authenticate_chef()

        url = reverse('chef-kitchen-availability')
        data = {
            'availability_type': 'custom',
            'days_of_week': ['monday', 'tuesday', 'wednesday'],
            'open_time': '09:00:00',
            'close_time': '21:00:00'
        }
        response = self.client.put(url, data, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('message', response.data)

# Example of how to run these tests:
# python manage.py test chef.test

from django.db import models
from django.core.exceptions import ValidationError
from uuid import uuid4
from authentication.models import User
from foods.models import Kitchen


class OrderCancellationReasonChoices(models.TextChoices):
    INGREDIENTS_NOT_AVAILABLE = ("ingredients_not_available", "Ingredients Not Available")
    KITCHEN_AT_FULL_CAPACITY = ("kitchen_at_full_capacity", "Kitchen At Full Capacity")
    MEAL_ITEM_DISCONTINUED = ("meal_item_discontinued", "Meal Item Discontinued")
    DUPLICATE_ORDER = ("duplicate_order", "Duplicate Order")
    SPECIAL_REQUEST_NOT_SUPPORTED = ("special_request_not_supported", "Special Request Not Supported")
    OTHERS = ("others", "Others")


class OrderCancellationReason(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    order = models.OneToOneField('order.Order', on_delete=models.CASCADE, related_name='cancellation_reason')
    reason = models.CharField(max_length=50, choices=OrderCancellationReasonChoices.choices)
    custom_reason = models.TextField(blank=True, null=True)
    cancelled_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def clean(self):
        if self.reason == OrderCancellationReasonChoices.OTHERS and not self.custom_reason:
            raise ValidationError("Custom reason is required when reason is 'others'")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Order {self.order.id} - {self.get_reason_display()}"


class KitchenAvailabilityTypeChoices(models.TextChoices):
    ALWAYS_OPEN = ("always_open", "Always Open")
    STANDARD_HOURS = ("standard_hours", "Standard Hours")
    CUSTOM = ("custom", "Custom")


class DayOfWeekChoices(models.TextChoices):
    MONDAY = ("monday", "Monday")
    TUESDAY = ("tuesday", "Tuesday")
    WEDNESDAY = ("wednesday", "Wednesday")
    THURSDAY = ("thursday", "Thursday")
    FRIDAY = ("friday", "Friday")
    SATURDAY = ("saturday", "Saturday")
    SUNDAY = ("sunday", "Sunday")


class KitchenAvailabilitySettings(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    kitchen = models.OneToOneField(Kitchen, on_delete=models.CASCADE, related_name='availability_settings')
    availability_type = models.CharField(
        max_length=20,
        choices=KitchenAvailabilityTypeChoices.choices,
        default=KitchenAvailabilityTypeChoices.STANDARD_HOURS
    )
    # For custom availability
    days_of_week = models.JSONField(default=list, blank=True)  # List of day names
    open_time = models.TimeField(null=True, blank=True)
    close_time = models.TimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def clean(self):
        if self.availability_type == KitchenAvailabilityTypeChoices.CUSTOM:
            if not self.days_of_week or not self.open_time or not self.close_time:
                raise ValidationError(
                    "Days of week, open time, and close time are required for custom availability"
                )
            # Validate days_of_week contains valid day names
            valid_days = [choice[0] for choice in DayOfWeekChoices.choices]
            for day in self.days_of_week:
                if day not in valid_days:
                    raise ValidationError(f"Invalid day: {day}")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.kitchen.name} - {self.get_availability_type_display()}"

from rest_framework import generics, status
from rest_framework.response import Response

from authentication.permissions import Is<PERSON>he<PERSON>
from chef.serializers import ChefFoodSerializerIn, ChefFoodSerializerOut, ChangeKitchenAvailabilitySerializer
from foods.serializers import GetChefKitchenSerializer


class ChefAddMealView(generics.CreateAPIView):
    serializer_class = ChefFoodSerializerIn
    permission_classes = [IsChef]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        added_food_item = serializer.save()

        serializer = ChefFoodSerializerOut(added_food_item)

        return Response({
            'message': 'Food added successfully',
            'data': serializer.data
        }, status=status.HTTP_201_CREATED)


class ChangeKitchenAvailabilityView(generics.UpdateAPIView):
    serializer_class = ChangeKitchenAvailabilitySerializer
    permission_classes = [IsChef]

    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        kitchen = serializer.save()

        serializer = GetChefKitchenSerializer(kitchen)

        return Response({
            'message': 'Kitchen status updated successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)






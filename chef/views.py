from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes as permission_classes_decorator
from rest_framework.response import Response
from django.shortcuts import get_object_or_404
from django.db.models import Q, Count
from django.db import transaction
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend

from authentication.permissions import IsChef
from order.models import Order, OrderStatusChoices, FoodReview, OrderTracking
from foods.models import Food, Kitchen
from rider.models import DispatchDriver
from .models import OrderCancellationReason, KitchenAvailabilitySettings
from .serializers import (
    ChefFoodSerializerIn, ChefFoodSerializerOut, ChangeKitchenAvailabilitySerializer,
    ChefOrderListSerializer, ChefOrderDetailSerializer, ChefFoodItemSerializer,
    OrderStatusUpdateSerializer, OrderCancellationSerializer, InTransitOrderSerializer,
    FoodReviewSerializer, AnalyticsDashboardSerializer, KitchenAvailabilitySettingsSerializer
)
from .filters import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ChefFoodFilter
from .permissions import <PERSON><PERSON><PERSON><PERSON>Own<PERSON>, CanManageOrder
from .utils import get_chef_from_user, get_orders_for_chef, get_foods_for_chef, get_chef_analytics, can_transition_order_status
from foods.serializers import GetChefKitchenSerializer


# Order Management Views
class ChefOrderListView(generics.ListAPIView):
    """
    List Chef's Orders with pagination and status filtering.
    GET /api/chef/orders/
    """
    serializer_class = ChefOrderListSerializer
    permission_classes = [IsChef]
    filter_backends = [DjangoFilterBackend]
    filterset_class = ChefOrderFilter

    def get_queryset(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return Order.objects.none()

        return get_orders_for_chef(chef_user)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())

        # Get status counts
        chef_user = get_chef_from_user(request.user)
        all_orders = get_orders_for_chef(chef_user) if chef_user else Order.objects.none()

        status_counts = {}
        for status_choice in OrderStatusChoices.choices:
            status_key = status_choice[0]
            status_counts[status_key] = all_orders.filter(status=status_key).count()

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
            response.data['status_counts'] = status_counts
            return response

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'results': serializer.data,
            'status_counts': status_counts
        })


class ChefOrderDetailView(generics.RetrieveAPIView):
    """
    Get Single Order Detail for Chef.
    GET /api/chef/orders/{order_id}/
    """
    serializer_class = ChefOrderDetailSerializer
    permission_classes = [IsChef, CanManageOrder]

    def get_queryset(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return Order.objects.none()

        return get_orders_for_chef(chef_user)


# Food/Menu Item Management Views
class ChefFoodListView(generics.ListAPIView):
    """
    List Chef's Food Items with pagination and status filtering.
    GET /api/chef/foods/
    """
    serializer_class = ChefFoodItemSerializer
    permission_classes = [IsChef]
    filter_backends = [DjangoFilterBackend]
    filterset_class = ChefFoodFilter

    def get_queryset(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return Food.objects.none()

        return get_foods_for_chef(chef_user)


class ChefFoodDetailView(generics.RetrieveAPIView):
    """
    Get Single Food Item Detail for Chef.
    GET /api/chef/foods/{food_id}/
    """
    serializer_class = ChefFoodItemSerializer
    permission_classes = [IsChef, IsChefOwner]

    def get_queryset(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return Food.objects.none()

        return get_foods_for_chef(chef_user)


# Order Status Management Views
class OrderStatusUpdateView(generics.UpdateAPIView):
    """
    Update Order Status.
    PATCH /api/chef/orders/{order_id}/status/
    """
    serializer_class = OrderStatusUpdateSerializer
    permission_classes = [IsChef, CanManageOrder]

    def get_queryset(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return Order.objects.none()

        return get_orders_for_chef(chef_user)

    def update(self, request, *args, **kwargs):
        order = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        new_status = serializer.validated_data['status']
        rider = serializer.validated_data.get('rider')

        # Check if transition is valid
        is_valid, message = can_transition_order_status(order, new_status, request.user)
        if not is_valid:
            return Response({'error': message}, status=status.HTTP_400_BAD_REQUEST)

        with transaction.atomic():
            # Update order status
            order.status = new_status
            order.save()

            # If transitioning to ready_for_pickup, assign rider and create tracking
            if new_status == OrderStatusChoices.READY_FOR_PICKUP and rider:
                # Update order status to with_dispatch_rider
                order.status = OrderStatusChoices.WITH_DISPATCH_RIDER
                order.save()

                # Create or update order tracking
                tracking, created = OrderTracking.objects.get_or_create(
                    order=order,
                    defaults={
                        'rider': rider,
                        'tracking_id': f"TRK-{order.unique_id or order.id}",
                        'status': 'confirmed'
                    }
                )
                if not created:
                    tracking.rider = rider
                    tracking.save()

        return Response({
            'message': f'Order status updated to {order.get_status_display()}',
            'order': ChefOrderDetailSerializer(order).data
        })


@api_view(['POST'])
@permission_classes_decorator([IsChef, CanManageOrder])
def cancel_order(request, order_id):
    """
    Cancel Order with Reason.
    POST /api/chef/orders/{order_id}/cancel/
    """
    chef_user = get_chef_from_user(request.user)
    if not chef_user:
        return Response({'error': 'Chef profile not found'}, status=status.HTTP_400_BAD_REQUEST)

    order = get_object_or_404(get_orders_for_chef(chef_user), id=order_id)

    # Check if order can be cancelled
    if order.status not in [OrderStatusChoices.NEW, OrderStatusChoices.PROCESSING]:
        return Response({
            'error': 'Order cannot be cancelled in current status'
        }, status=status.HTTP_400_BAD_REQUEST)

    serializer = OrderCancellationSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    with transaction.atomic():
        # Update order status
        order.status = OrderStatusChoices.CANCELLED
        order.cancelled_at = timezone.now()
        order.save()

        # Create cancellation reason
        OrderCancellationReason.objects.create(
            order=order,
            reason=serializer.validated_data['reason'],
            custom_reason=serializer.validated_data.get('custom_reason', ''),
            cancelled_by=request.user
        )

    return Response({
        'message': 'Order cancelled successfully',
        'order': ChefOrderDetailSerializer(order).data
    })


# Order Tracking Views
class InTransitOrdersView(generics.ListAPIView):
    """
    Track In-Transit Orders.
    GET /api/chef/orders/in-transit/
    """
    serializer_class = InTransitOrderSerializer
    permission_classes = [IsChef]

    def get_queryset(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return Order.objects.none()

        return get_orders_for_chef(chef_user).filter(
            status=OrderStatusChoices.WITH_DISPATCH_RIDER
        )


# Reviews Management Views
class ChefReviewsView(generics.ListAPIView):
    """
    View Food Reviews for Chef's Foods.
    GET /api/chef/reviews/
    """
    serializer_class = FoodReviewSerializer
    permission_classes = [IsChef]

    def get_queryset(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return FoodReview.objects.none()

        try:
            chef_kitchen = chef_user.kitchen
            return FoodReview.objects.filter(
                food__kitchen=chef_kitchen
            ).order_by('-created_at')
        except AttributeError:
            return FoodReview.objects.none()


# Analytics Dashboard Views
@api_view(['GET'])
@permission_classes_decorator([IsChef])
def analytics_dashboard(request):
    """
    Sales Dashboard Analytics.
    GET /api/chef/dashboard/analytics/
    """
    chef_user = get_chef_from_user(request.user)
    if not chef_user:
        return Response({'error': 'Chef profile not found'}, status=status.HTTP_400_BAD_REQUEST)

    period = request.GET.get('period', 'daily')
    if period not in ['daily', 'weekly', 'monthly', 'yearly']:
        return Response({'error': 'Invalid period. Must be daily, weekly, monthly, or yearly'},
                       status=status.HTTP_400_BAD_REQUEST)

    analytics_data = get_chef_analytics(chef_user, period)
    serializer = AnalyticsDashboardSerializer(analytics_data)

    return Response({
        'period': period,
        'analytics': serializer.data
    })


# Kitchen Availability Settings Views
class KitchenAvailabilityView(generics.RetrieveUpdateAPIView):
    """
    Kitchen Availability Settings.
    GET/PUT /api/chef/kitchen/availability/
    """
    serializer_class = KitchenAvailabilitySettingsSerializer
    permission_classes = [IsChef]

    def get_object(self):
        chef_user = get_chef_from_user(self.request.user)
        if not chef_user:
            return None

        # Get the chef's kitchen (OneToOne relationship)
        try:
            kitchen = chef_user.kitchen
        except AttributeError:
            return None

        settings, created = KitchenAvailabilitySettings.objects.get_or_create(
            kitchen=kitchen,
            defaults={
                'availability_type': 'standard_hours'
            }
        )
        return settings

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            return Response({'error': 'Kitchen not found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            return Response({'error': 'Kitchen not found'}, status=status.HTTP_404_NOT_FOUND)

        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response({
            'message': 'Kitchen availability settings updated successfully',
            'data': serializer.data
        })


# Legacy Views (keeping for backward compatibility)
class ChefAddMealView(generics.CreateAPIView):
    serializer_class = ChefFoodSerializerIn
    permission_classes = [IsChef]

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        added_food_item = serializer.save()

        serializer = ChefFoodSerializerOut(added_food_item)

        return Response({
            'message': 'Food added successfully',
            'data': serializer.data
        }, status=status.HTTP_201_CREATED)


class ChangeKitchenAvailabilityView(generics.UpdateAPIView):
    serializer_class = ChangeKitchenAvailabilitySerializer
    permission_classes = [IsChef]

    def update(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        kitchen = serializer.save()

        serializer = GetChefKitchenSerializer(kitchen)

        return Response({
            'message': 'Kitchen status updated successfully',
            'data': serializer.data
        }, status=status.HTTP_200_OK)






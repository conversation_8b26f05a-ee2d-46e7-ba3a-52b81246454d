# Generated by Django 5.1.7 on 2025-08-01 22:19

import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('foods', '0004_food_status'),
        ('order', '0004_order_unique_id_alter_order_status_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='KitchenAvailabilitySettings',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('availability_type', models.CharField(choices=[('always_open', 'Always Open'), ('standard_hours', 'Standard Hours'), ('custom', 'Custom')], default='standard_hours', max_length=20)),
                ('days_of_week', models.JSONField(blank=True, default=list)),
                ('open_time', models.TimeField(blank=True, null=True)),
                ('close_time', models.TimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('kitchen', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='availability_settings', to='foods.kitchen')),
            ],
        ),
        migrations.CreateModel(
            name='OrderCancellationReason',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('reason', models.CharField(choices=[('ingredients_not_available', 'Ingredients Not Available'), ('kitchen_at_full_capacity', 'Kitchen At Full Capacity'), ('meal_item_discontinued', 'Meal Item Discontinued'), ('duplicate_order', 'Duplicate Order'), ('special_request_not_supported', 'Special Request Not Supported'), ('others', 'Others')], max_length=50)),
                ('custom_reason', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cancelled_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('order', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='cancellation_reason', to='order.order')),
            ],
        ),
    ]

from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from user_profiles.models import ChefUser
from foods.models import Kitchen, Food
from order.models import Order, FoodTray, FoodTrayItem, CheckoutBill, DeliveryAddress
from chef.utils import get_chef_analytics, get_orders_for_chef, get_foods_for_chef

User = get_user_model()


class Command(BaseCommand):
    help = 'Test Chef API ORM queries'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Testing Chef API ORM queries...'))
        
        # Find a chef user
        chef_user = User.objects.filter(user_type='chef').first()
        if not chef_user:
            self.stdout.write(self.style.ERROR('No chef user found. Please create a chef user first.'))
            return
        
        self.stdout.write(f'Using chef user: {chef_user.email}')
        
        try:
            # Test get_chef_from_user
            from chef.utils import get_chef_from_user
            chef_profile = get_chef_from_user(chef_user)
            if not chef_profile:
                self.stdout.write(self.style.ERROR('Chef profile not found'))
                return
            
            self.stdout.write(f'✓ Chef profile found: {chef_profile}')
            
            # Test kitchen relationship
            try:
                kitchen = chef_profile.kitchen
                self.stdout.write(f'✓ Kitchen found: {kitchen.name}')
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Kitchen not found: {e}'))
                return
            
            # Test get_foods_for_chef
            foods = get_foods_for_chef(chef_profile)
            self.stdout.write(f'✓ Found {foods.count()} food items')
            
            # Test get_orders_for_chef
            orders = get_orders_for_chef(chef_profile)
            self.stdout.write(f'✓ Found {orders.count()} orders')
            
            # Test analytics
            analytics = get_chef_analytics(chef_profile, 'daily')
            self.stdout.write(f'✓ Analytics: {analytics["orders_received_count"]} orders received')
            
            # Test the specific query that was failing
            chef_foods = Food.objects.filter(kitchen=kitchen)
            test_orders = Order.objects.filter(
                payment__food_tray__foodtrayitem_set__food__in=chef_foods
            ).distinct()
            
            self.stdout.write(f'✓ Direct query test: Found {test_orders.count()} orders')
            
            self.stdout.write(self.style.SUCCESS('🎉 All tests passed!'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Test failed: {e}'))
            import traceback
            traceback.print_exc()

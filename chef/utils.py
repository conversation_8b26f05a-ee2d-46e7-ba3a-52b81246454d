from django.db.models import Count, Avg, Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from order.models import Order, OrderStatusChoices, FoodReview, CheckoutBill, FoodTray, FoodTrayItem
from foods.models import Food
from user_profiles.models import ChefUser


def get_chef_analytics(chef_user, period='daily'):
    """
    Get analytics data for a chef based on the specified period.
    """
    # Get chef's kitchen (OneToOne relationship)
    try:
        chef_kitchen = chef_user.kitchen
    except AttributeError:
        return {
            'orders_received_count': 0,
            'orders_processed_count': 0,
            'orders_delivered_count': 0,
            'orders_delivered_amount': 0.0,
            'top_review': None,
            'top_rated_food': None,
            'least_rated_food': None,
            'most_cancelled_food': None,
        }

    # Calculate date range based on period
    now = timezone.now()
    if period == 'daily':
        start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
    elif period == 'weekly':
        start_date = now - timedelta(days=7)
    elif period == 'monthly':
        start_date = now - timedelta(days=30)
    elif period == 'yearly':
        start_date = now - timedelta(days=365)
    else:
        start_date = now - timedelta(days=1)  # Default to daily

    # Get chef's food items first
    chef_foods_items = FoodTrayItem.objects.filter(food__kitchen=chef_kitchen)
    chef_foods = [item.food_tray for item in chef_foods_items]

    # Get orders that contain any of the chef's food items
    chef_orders = Order.objects.filter(payment__food_tray__in=chef_foods, created_at__gte=start_date).distinct()
    
    # Calculate metrics
    orders_received_count = chef_orders.count()
    orders_processed_count = chef_orders.filter(
        status__in=[OrderStatusChoices.PROCESSING, OrderStatusChoices.READY_FOR_PICKUP]
    ).count()
    orders_delivered_count = chef_orders.filter(status=OrderStatusChoices.DELIVERED).count()
    
    # Calculate total revenue from delivered orders
    delivered_orders = chef_orders.filter(status=OrderStatusChoices.DELIVERED)
    orders_delivered_amount = sum(
        order.payment.total for order in delivered_orders if order.payment
    )
    
    # Get chef's food items
    chef_foods = Food.objects.filter(kitchen=chef_kitchen)

    # Get top review
    top_review = FoodReview.objects.filter(
        food__kitchen=chef_kitchen,
        created_at__gte=start_date
    ).order_by('-rating', '-created_at').first()
    
    # Get food statistics
    food_stats = chef_foods.annotate(
        avg_rating=Avg('order_reviews__rating'),
        review_count=Count('order_reviews'),
        order_count=Count('foodtrayitem__food_tray__checkoutbill__order', distinct=True)
    )
    
    # Top rated food
    top_rated_food = food_stats.filter(avg_rating__isnull=False).order_by('-avg_rating').first()
    
    # Least rated food
    least_rated_food = food_stats.filter(avg_rating__isnull=False).order_by('avg_rating').first()
    
    # Most cancelled food (approximation based on orders)
    cancelled_orders = chef_orders.filter(status=OrderStatusChoices.CANCELLED)
    most_cancelled_food = None
    if cancelled_orders.exists():
        # This is a simplified approach - in reality, you'd need to track which specific foods were cancelled
        food_cancellation_counts = {}
        for order in cancelled_orders:
            if order.payment and order.payment.food_tray:
                for item in order.payment.food_tray.foodtrayitem_set.all():
                    if item.food.kitchen == chef_kitchen:
                        food_id = item.food.id
                        food_cancellation_counts[food_id] = food_cancellation_counts.get(food_id, 0) + 1
        
        if food_cancellation_counts:
            most_cancelled_food_id = max(food_cancellation_counts, key=food_cancellation_counts.get)
            most_cancelled_food = chef_foods.filter(id=most_cancelled_food_id).first()
    
    return {
        'orders_received_count': orders_received_count,
        'orders_processed_count': orders_processed_count,
        'orders_delivered_count': orders_delivered_count,
        'orders_delivered_amount': float(orders_delivered_amount) if orders_delivered_amount else 0.0,
        'top_review': top_review,
        'top_rated_food': top_rated_food,
        'least_rated_food': least_rated_food,
        'most_cancelled_food': most_cancelled_food,
    }


def get_chef_from_user(user):
    """
    Get ChefUser instance from User instance.
    """
    try:
        return user.chef
    except ChefUser.DoesNotExist:
        return None


def get_orders_for_chef(chef_user, status=None):
    """
    Get orders that contain food items from chef's kitchen.
    """
    try:
        chef_kitchen = chef_user.kitchen
    except AttributeError:
        return Order.objects.none()

    # Get chef's food items first
    chef_foods_items = FoodTrayItem.objects.filter(food__kitchen=chef_kitchen)
    chef_foods = [item.food_tray for item in chef_foods_items]

    # Get orders that contain any of the chef's food items
    orders = Order.objects.filter(payment__food_tray__in=chef_foods).distinct().order_by('-created_at')
    
    if status:
        orders = orders.filter(status=status)
    
    return orders


def get_foods_for_chef(chef_user, status=None):
    """
    Get food items created by the chef.
    """
    try:
        chef_kitchen = chef_user.kitchen
    except AttributeError:
        return Food.objects.none()

    foods = Food.objects.filter(kitchen=chef_kitchen).order_by('-created_at')
    
    if status:
        foods = foods.filter(status=status)
    
    return foods


def can_transition_order_status(order, new_status, user):
    """
    Check if an order status transition is valid.
    """
    current_status = order.status
    
    # Define valid transitions
    valid_transitions = {
        OrderStatusChoices.NEW: [OrderStatusChoices.PROCESSING, OrderStatusChoices.CANCELLED],
        OrderStatusChoices.PROCESSING: [OrderStatusChoices.READY_FOR_PICKUP, OrderStatusChoices.CANCELLED],
        OrderStatusChoices.READY_FOR_PICKUP: [OrderStatusChoices.WITH_DISPATCH_RIDER],
        OrderStatusChoices.WITH_DISPATCH_RIDER: [OrderStatusChoices.DELIVERED],
    }
    
    # Check if transition is valid
    if new_status not in valid_transitions.get(current_status, []):
        return False, f"Cannot transition from {current_status} to {new_status}"
    
    # Additional checks for specific transitions
    if new_status == OrderStatusChoices.READY_FOR_PICKUP:
        # This transition might require rider assignment (handled in the view)
        pass
    
    return True, "Transition is valid"

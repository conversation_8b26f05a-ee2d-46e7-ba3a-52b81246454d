import django_filters
from django.db.models import Q
from order.models import Order, OrderStatusChoices
from foods.models import Food


class ChefOrderFilter(django_filters.FilterSet):
    status = django_filters.ChoiceFilter(
        choices=OrderStatusChoices.choices,
        field_name='status',
        help_text="Filter orders by status"
    )
    
    class Meta:
        model = Order
        fields = ['status']


class ChefFoodFilter(django_filters.FilterSet):
    status = django_filters.ChoiceFilter(
        choices=Food.STATUS.choices,
        field_name='status',
        help_text="Filter foods by approval status"
    )
    
    class Meta:
        model = Food
        fields = ['status']

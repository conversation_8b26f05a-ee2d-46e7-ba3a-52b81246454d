from rest_framework import serializers
from django.db.models import Avg, Count
from django.utils import timezone
from foods.models import Food, Kitchen
from order.models import Order, OrderStatusChoices, FoodReview, DeliveryAddress, CheckoutBill, FoodTrayItem
from rider.models import DispatchDriver
from authentication.models import User
from user_profiles.models import ChefUser
from .models import OrderCancellationReason, OrderCancellationReasonChoices, KitchenAvailabilitySettings
from .utils import get_chef_analytics


# Customer and Address Serializers for Order Details
class CustomerSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'email', 'phone_number']


class DeliveryAddressSerializer(serializers.ModelSerializer):
    class Meta:
        model = DeliveryAddress
        fields = ['id', 'address', 'state', 'latitude', 'longitude', 'is_default']


# Food Item Serializers
class ChefFoodItemSerializer(serializers.ModelSerializer):
    kitchen_name = serializers.Char<PERSON>ield(source='kitchen.name', read_only=True)
    food_categories = serializers.ReadOnlyField()
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()

    class Meta:
        model = Food
        fields = [
            'id', 'name', 'description', 'price', 'display_image', 'status',
            'dietary', 'cuisine', 'preparation_type', 'preparation_time_minutes',
            'breakfast', 'lunch', 'dinner', 'dessert_snacks', 'drinks',
            'kitchen_name', 'food_categories', 'average_rating', 'review_count',
            'created_at', 'updated_at'
        ]

    def get_average_rating(self, obj):
        return obj.order_reviews.aggregate(avg_rating=Avg('rating'))['avg_rating'] or 0.0

    def get_review_count(self, obj):
        return obj.order_reviews.count()


class FoodTrayItemSerializer(serializers.ModelSerializer):
    food = ChefFoodItemSerializer(read_only=True)

    class Meta:
        model = FoodTrayItem
        fields = ['id', 'food', 'quantity', 'price']


class CheckoutBillSerializer(serializers.ModelSerializer):
    items = serializers.SerializerMethodField()

    class Meta:
        model = CheckoutBill
        fields = [
            'id', 'item_total', 'discount', 'delivery_fee', 'service_fee', 'total',
            'payment_mode', 'paid_at', 'items'
        ]

    def get_items(self, obj):
        if obj.food_tray:
            return FoodTrayItemSerializer(obj.food_tray.foodtrayitem_set.all(), many=True).data
        return []


# Order Serializers
class ChefOrderListSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(source='user', read_only=True)
    delivery_address = DeliveryAddressSerializer(source='address', read_only=True)
    item_count = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'unique_id', 'status', 'customer', 'delivery_address',
            'contact_phone', 'prep_instruction', 'item_count', 'total_amount',
            'created_at', 'updated_at', 'delivered_at', 'cancelled_at'
        ]

    def get_item_count(self, obj):
        if obj.payment and obj.payment.food_tray:
            return obj.payment.food_tray.items.count()
        return 0

    def get_total_amount(self, obj):
        if obj.payment:
            return obj.payment.total
        return 0.0


class ChefOrderDetailSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(source='user', read_only=True)
    delivery_address = DeliveryAddressSerializer(source='address', read_only=True)
    payment_details = CheckoutBillSerializer(source='payment', read_only=True)
    cancellation_reason = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'unique_id', 'status', 'customer', 'delivery_address',
            'payment_details', 'contact_phone', 'prep_instruction',
            'cancellation_reason', 'created_at', 'updated_at',
            'delivered_at', 'cancelled_at'
        ]

    def get_cancellation_reason(self, obj):
        if hasattr(obj, 'cancellation_reason'):
            return {
                'reason': obj.cancellation_reason.reason,
                'custom_reason': obj.cancellation_reason.custom_reason,
                'cancelled_by': obj.cancellation_reason.cancelled_by.email
            }
        return None


# Order Status Management Serializers
class OrderStatusUpdateSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=[
        (OrderStatusChoices.PROCESSING, 'Processing'),
        (OrderStatusChoices.READY_FOR_PICKUP, 'Ready For Pickup'),
    ])
    rider_id = serializers.UUIDField(required=False, allow_null=True)
    rider_username = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        status = attrs['status']
        rider_id = attrs.get('rider_id')
        rider_username = attrs.get('rider_username')

        # If transitioning to ready_for_pickup, require rider assignment
        if status == OrderStatusChoices.READY_FOR_PICKUP:
            if not rider_id and not rider_username:
                raise serializers.ValidationError(
                    "Either rider_id or rider_username is required when setting status to ready_for_pickup"
                )

            # Validate rider exists
            rider = None
            if rider_id:
                try:
                    rider = DispatchDriver.objects.get(id=rider_id, is_active=True)
                except DispatchDriver.DoesNotExist:
                    raise serializers.ValidationError("Rider with provided ID not found or inactive")
            elif rider_username:
                try:
                    rider = DispatchDriver.objects.get(user__email=rider_username, is_active=True)
                except DispatchDriver.DoesNotExist:
                    raise serializers.ValidationError("Rider with provided username not found or inactive")

            attrs['rider'] = rider

        return attrs


class OrderCancellationSerializer(serializers.Serializer):
    reason = serializers.ChoiceField(choices=OrderCancellationReasonChoices.choices)
    custom_reason = serializers.CharField(required=False, allow_blank=True)

    def validate(self, attrs):
        reason = attrs['reason']
        custom_reason = attrs.get('custom_reason', '')

        if reason == OrderCancellationReasonChoices.OTHERS and not custom_reason:
            raise serializers.ValidationError(
                "custom_reason is required when reason is 'others'"
            )

        return attrs


# Rider and Tracking Serializers
class RiderSerializer(serializers.ModelSerializer):
    name = serializers.CharField(source='user.get_full_name', read_only=True)
    email = serializers.CharField(source='user.email', read_only=True)
    phone = serializers.CharField(source='user.phone_number', read_only=True)

    class Meta:
        model = DispatchDriver
        fields = ['id', 'name', 'email', 'phone', 'vehicle_type', 'is_active']


class InTransitOrderSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(source='user', read_only=True)
    delivery_address = DeliveryAddressSerializer(source='address', read_only=True)
    rider = serializers.SerializerMethodField()
    estimated_delivery_time = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = [
            'id', 'unique_id', 'customer', 'delivery_address', 'rider',
            'estimated_delivery_time', 'total_amount', 'contact_phone',
            'created_at', 'updated_at'
        ]

    def get_rider(self, obj):
        # Get rider from order tracking
        tracking = obj.ordertracking_set.first()
        if tracking and tracking.rider:
            return RiderSerializer(tracking.rider).data
        return None

    def get_estimated_delivery_time(self, obj):
        # Simple estimation: 30 minutes from when order was marked as with_dispatch_rider
        # In a real app, this would be more sophisticated
        from datetime import timedelta
        if obj.status == OrderStatusChoices.WITH_DISPATCH_RIDER:
            return obj.updated_at + timedelta(minutes=30)
        return None

    def get_total_amount(self, obj):
        if obj.payment:
            return obj.payment.total
        return 0.0


# Review Serializers
class FoodReviewSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(source='user', read_only=True)
    food = ChefFoodItemSerializer(read_only=True)
    order_id = serializers.CharField(source='order.id', read_only=True)

    class Meta:
        model = FoodReview
        fields = [
            'id', 'customer', 'food', 'order_id', 'rating', 'review_text',
            'created_at', 'updated_at'
        ]


# Analytics Serializers
class TopReviewSerializer(serializers.ModelSerializer):
    customer = CustomerSerializer(source='user', read_only=True)
    food_name = serializers.CharField(source='food.name', read_only=True)

    class Meta:
        model = FoodReview
        fields = ['id', 'customer', 'food_name', 'rating', 'review_text', 'created_at']


class FoodAnalyticsSerializer(serializers.ModelSerializer):
    average_rating = serializers.SerializerMethodField()
    review_count = serializers.SerializerMethodField()
    cancellation_count = serializers.SerializerMethodField()

    class Meta:
        model = Food
        fields = ['id', 'name', 'average_rating', 'review_count', 'cancellation_count']

    def get_average_rating(self, obj):
        return obj.order_reviews.aggregate(avg_rating=Avg('rating'))['avg_rating'] or 0.0

    def get_review_count(self, obj):
        return obj.order_reviews.count()

    def get_cancellation_count(self, obj):
        # This is a simplified approach - in reality, you'd track cancellations more precisely
        return getattr(obj, 'cancellation_count', 0)


class AnalyticsDashboardSerializer(serializers.Serializer):
    orders_received_count = serializers.IntegerField()
    orders_processed_count = serializers.IntegerField()
    orders_delivered_count = serializers.IntegerField()
    orders_delivered_amount = serializers.FloatField()
    top_review = TopReviewSerializer(allow_null=True)
    top_rated_food = FoodAnalyticsSerializer(allow_null=True)
    least_rated_food = FoodAnalyticsSerializer(allow_null=True)
    most_cancelled_food = FoodAnalyticsSerializer(allow_null=True)


# Kitchen Availability Serializers
class KitchenAvailabilitySettingsSerializer(serializers.ModelSerializer):
    class Meta:
        model = KitchenAvailabilitySettings
        fields = [
            'id', 'availability_type', 'days_of_week', 'open_time', 'close_time',
            'created_at', 'updated_at'
        ]

    def validate(self, attrs):
        availability_type = attrs.get('availability_type')

        if availability_type == 'custom':
            days_of_week = attrs.get('days_of_week')
            open_time = attrs.get('open_time')
            close_time = attrs.get('close_time')

            if not days_of_week or not open_time or not close_time:
                raise serializers.ValidationError(
                    "days_of_week, open_time, and close_time are required for custom availability"
                )

        return attrs


# Legacy serializers (keeping for backward compatibility)
class ChefFoodSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = Food
        exclude = []


class ChefFoodSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    kitchen_id = serializers.UUIDField()
    image = serializers.ImageField()
    dietary_type = serializers.ChoiceField(choices=Food.DIETARY)
    cuisine_type = serializers.ChoiceField(choices=Food.CUISINE)
    preparation_type = serializers.ChoiceField(choices=Food.PREPARATION)
    preparation_duration = serializers.IntegerField()
    is_breakfast = serializers.BooleanField(default=False)
    is_lunch = serializers.BooleanField(default=False)
    is_dinner = serializers.BooleanField(default=False)
    is_dessert = serializers.BooleanField(default=False)
    is_drink = serializers.BooleanField(default=False)
    name = serializers.CharField(max_length=150)
    description = serializers.CharField(max_length=250)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)

    def validate(self, attrs):
        user = attrs['user']
        kitchen_id = attrs['kitchen_id']

        try:
            kitchen = Kitchen.objects.get(id=kitchen_id, chef__user=user)
        except Kitchen.DoesNotExist:
            raise serializers.ValidationError('Selected kitchen not found')

        if not kitchen.available:
            raise serializers.ValidationError('Selected kitchen is not active')

        return attrs

    def create(self, validated_data):
        user = validated_data['user']
        kitchen_id = validated_data['kitchen_id']
        image = validated_data['image']
        dietary_type = validated_data['dietary_type']
        cuisine_type = validated_data['cuisine_type']
        preparation_type = validated_data['preparation_type']
        preparation_duration = validated_data['preparation_duration']
        is_breakfast = validated_data['is_breakfast']
        is_lunch = validated_data['is_lunch']
        is_dinner = validated_data['is_dinner']
        is_dessert = validated_data['is_dessert']
        is_drink = validated_data['is_drink']
        name = validated_data['name']
        description = validated_data['description']
        amount = validated_data['amount']

        kitchen = Kitchen.objects.get(id=kitchen_id, chef__user=user)

        food = Food.objects.create(
            kitchen=kitchen, display_image=image, dietary=dietary_type, cuisine=cuisine_type, preparation_type=preparation_type,
            preparation_time_minutes=preparation_duration, breakfast=is_breakfast, lunch=is_lunch, dinner=is_dinner, dessert_snacks=is_dessert,
            drinks=is_drink, name=name, description=description, price=amount
        )

        return food


class ChangeKitchenAvailabilitySerializer(serializers.Serializer):
    kitchen_id = serializers.UUIDField()
    available = serializers.BooleanField()

    def validate(self, attrs):
        user = self.context['request'].user
        kitchen_id = attrs['kitchen_id']

        try:
            kitchen = Kitchen.objects.get(id=kitchen_id, chef__user=user)
        except Kitchen.DoesNotExist:
            raise serializers.ValidationError('Selected kitchen not found')

        return attrs

    def update(self, instance, validated_data):
        available = validated_data['available']
        instance.available = available
        instance.save()
        return instance



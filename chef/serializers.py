from rest_framework import serializers
from foods.models import Food, Kitchen


class ChefFoodSerializerOut(serializers.ModelSerializer):
    class Meta:
        model = Food
        exclude = []


class ChefFoodSerializerIn(serializers.Serializer):
    user = serializers.HiddenField(default=serializers.CurrentUserDefault())
    kitchen_id = serializers.UUIDField()
    image = serializers.ImageField()
    dietary_type = serializers.ChoiceField(choices=Food.DIETARY)
    cuisine_type = serializers.ChoiceField(choices=Food.CUISINE)
    preparation_type = serializers.ChoiceField(choices=Food.PREPARATION)
    preparation_duration = serializers.IntegerField()
    is_breakfast = serializers.BooleanField(default=False)
    is_lunch = serializers.BooleanField(default=False)
    is_dinner = serializers.BooleanField(default=False)
    is_dessert = serializers.BooleanField(default=False)
    is_drink = serializers.BooleanField(default=False)
    name = serializers.Char<PERSON><PERSON>(max_length=150)
    description = serializers.CharField(max_length=250)
    amount = serializers.DecimalField(max_digits=10, decimal_places=2)

    def validate(self, attrs):
        user = attrs['user']
        kitchen_id = attrs['kitchen_id']

        try:
            kitchen = Kitchen.objects.get(id=kitchen_id, chef__user=user)
        except Kitchen.DoesNotExist:
            raise serializers.ValidationError('Selected kitchen not found')

        if not kitchen.available:
            raise serializers.ValidationError('Selected kitchen is not active')

        return attrs

    def create(self, validated_data):
        user = validated_data['user']
        kitchen_id = validated_data['kitchen_id']
        image = validated_data['image']
        dietary_type = validated_data['dietary_type']
        cuisine_type = validated_data['cuisine_type']
        preparation_type = validated_data['preparation_type']
        preparation_duration = validated_data['preparation_duration']
        is_breakfast = validated_data['is_breakfast']
        is_lunch = validated_data['is_lunch']
        is_dinner = validated_data['is_dinner']
        is_dessert = validated_data['is_dessert']
        is_drink = validated_data['is_drink']
        name = validated_data['name']
        description = validated_data['description']
        amount = validated_data['amount']

        kitchen = Kitchen.objects.get(id=kitchen_id, chef__user=user)

        food = Food.objects.create(
            kitchen=kitchen, display_image=image, dietary=dietary_type, cuisine=cuisine_type, preparation_type=preparation_type,
            preparation_time_minutes=preparation_duration, breakfast=is_breakfast, lunch=is_lunch, dinner=is_dinner, dessert_snacks=is_dessert,
            drinks=is_drink, name=name, description=description, price=amount
        )

        return food


class ChangeKitchenAvailabilitySerializer(serializers.Serializer):
    kitchen_id = serializers.UUIDField()
    available = serializers.BooleanField()

    def validate(self, attrs):
        user = self.context['request'].user
        kitchen_id = attrs['kitchen_id']

        try:
            kitchen = Kitchen.objects.get(id=kitchen_id, chef__user=user)
        except Kitchen.DoesNotExist:
            raise serializers.ValidationError('Selected kitchen not found')

        return attrs

    def update(self, instance, validated_data):
        available = validated_data['available']
        instance.available = available
        instance.save()
        return instance



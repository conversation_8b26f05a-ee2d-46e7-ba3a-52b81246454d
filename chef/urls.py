from django.urls import path
from . import views

urlpatterns = [
    # Order Management Endpoints
    path('orders/', views.ChefOrderListView.as_view(), name='chef-orders-list'),
    path('orders/<uuid:pk>/', views.ChefOrderDetailView.as_view(), name='chef-order-detail'),

    # Food/Menu Item Management
    path('foods/', views.ChefFoodListView.as_view(), name='chef-foods-list'),
    path('foods/<uuid:pk>/', views.ChefFoodDetailView.as_view(), name='chef-food-detail'),

    # Order Status Management
    path('orders/<uuid:pk>/status/', views.OrderStatusUpdateView.as_view(), name='chef-order-status-update'),
    path('orders/<uuid:order_id>/cancel/', views.cancel_order, name='chef-order-cancel'),

    # Order Tracking
    path('orders/in-transit/', views.InTransitOrdersView.as_view(), name='chef-orders-in-transit'),

    # Reviews Management
    path('reviews/', views.ChefReviewsView.as_view(), name='chef-reviews'),

    # Analytics Dashboard
    path('dashboard/analytics/', views.analytics_dashboard, name='chef-analytics-dashboard'),

    # Kitchen Availability Settings
    path('kitchen/availability/', views.KitchenAvailabilityView.as_view(), name='chef-kitchen-availability'),

    # Legacy endpoints (keeping for backward compatibility)
    path('meal/add/', views.ChefAddMealView.as_view(), name='add-meal'),
    path('kitchen/update-status/', views.ChangeKitchenAvailabilityView.as_view(), name='change-kitchen-availability'),
]

from rest_framework.parsers import <PERSON>Part<PERSON>ars<PERSON>, DataAndFiles
from rest_framework.exceptions import ParseError


class NestedMultiPartParser(MultiPartParser):
    """
    Robust parser for nested multipart form data.
    Handles:
    - Keys like `files[0][meta][type]`
    - Empty brackets (`files[]` → auto-incremented indices)
    - Proper dict/list conversion
    - Preserves original files
    """

    def parse(self, stream, media_type=None, parser_context=None):
        parsed = super().parse(stream, media_type, parser_context)
        nested_data = {}

        # Process all keys (data + files)
        for key in list(parsed.data.keys()) + list(parsed.files.keys()):
            values = parsed.files.getlist(key) or parsed.data.getlist(key)
            for value in values:
                self._assign_nested_value(nested_data, key, value)

        return DataAndFiles(
            # data=self._normalize_structure(nested_data),
            data=nested_data,
            files=parsed.files  # Preserve original files
        )
    
    def _assign_nested_value(self, root, compound_key, value):
        """Assigns a value to a nested structure, creating containers as needed."""
        keys = self._split_key(compound_key)
        current = root
        parent = None
        parent_key = None

        for idx, key in enumerate(keys):
            is_last = idx == len(keys) - 1
            next_key = keys[idx+1] if is_last is False else None
            
            # Handle empty brackets
            if key == "":
                key = len(current) if isinstance(current, list) else 0

            # Convert numeric keys
            if isinstance(key, str) and key.isdigit():
                key = int(key)

            if isinstance(key, int):
                if not isinstance(current, (list, dict)):
                    # Convert unexpected types to dict if parent exists
                    if parent is not None:
                        current = self._create_default_container(is_last, next_key)
                        parent[parent_key] = current
                    else:
                        raise ParseError(
                            f"Root structure is of type {type(current)}"
                        )
                
                if isinstance(current, list):
                    # Expand list if needed
                    while len(current) <= key:
                        current.append(self._create_default_container(is_last, next_key))
                    
                    if is_last:
                        current[key] = value
                    else:
                        if not isinstance(current[key], (dict, list)):
                            current[key] = self._create_default_container(is_last, next_key)
                else:
                    # Handle dict with int keys
                    if is_last:
                        current[key] = value
                    elif key not in current or not isinstance(current[key], (dict, list)):
                        current[key] = self._create_default_container(is_last, next_key)
            else:
                # String key handling
                if not isinstance(current, dict):
                    if parent is not None:
                        # Convert to dict if we have a parent reference
                        current = {}
                        parent[parent_key] = current
                    else:
                        raise ParseError(
                            f"Cannot use string key '{key}' at root level when structure is {type(current)}"
                        )
                
                if is_last:
                    current[key] = value
                elif key not in current or not isinstance(current[key], (dict, list)):
                    current[key] = self._create_default_container(is_last, next_key)

            parent = current
            parent_key = key
            current = current[key]

    def _create_default_container(self, is_last, next_key=None):
        """Returns [] if next key is numeric, {} otherwise (unless it's a leaf node)."""
        if is_last:
            return None
        if next_key is not None:
            if isinstance(next_key, int) or next_key=='' or (isinstance(next_key, str) and next_key.isdigit()):
                return []
        return {}
    
    def _split_key(self, key):
        """
        Splits keys into segments, handling:
        - Bracket notation: 'files[0][meta][type]' → ['files', '0', 'meta', 'type']
        - Dot notation: 'files.0.meta.type' → ['files', '0', 'meta', 'type']
        - Empty brackets: 'files[]' → ['files', '']
        - Mixed notation (though not recommended): 'files[0].meta' → ['files', '0', 'meta']
        """
        segments = []
        remaining = key
        pattern = re.compile(r'^([^\[\].]+)|\[([^\[\]]*)\]|\.([^\[\].]*)')

        while remaining:
            match = pattern.match(remaining)
            if not match:
                raise ParseError(f"Malformed key: {key}")

            # Determine which group matched and handle empty cases
            segment = None
            if match.group(1) is not None:  # Initial key segment
                segment = match.group(1)
            elif match.group(2) is not None:  # Bracket notation
                segment = match.group(2)  # Returns empty string for []
            elif match.group(3) is not None:  # Dot notation
                segment = match.group(3) or ""  # Convert None to "" for . notation

            segments.append(segment)
            remaining = remaining[match.end():]

        if not segments:
            raise ParseError(f"Empty key: {key}")

        return segments

    def _convert_dict_to_list(self, d):
        """Converts a dict with sequential int keys to a list."""
        keys = [k for k in d.keys() if isinstance(k, (int, str))]
        int_keys = []
        
        for k in keys:
            if isinstance(k, str) and k.isdigit():
                int_keys.append(int(k))
            elif isinstance(k, int):
                int_keys.append(k)
        
        if int_keys and int_keys == list(range(min(int_keys), max(int_keys) + 1)):
            return [d[k] for k in sorted(int_keys)]
        
        return d
    


            
    


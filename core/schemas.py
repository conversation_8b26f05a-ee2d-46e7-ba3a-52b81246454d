from drf_spectacular.openapi import AutoSchema

class CustomAutoSchema(AutoSchema):
    def get_tags(self):
        tokenized_path = self._tokenize_path()
        try:
            tag = tokenized_path[2]
            if tag.lower() == "admin" and len(tokenized_path) > 3:
                return [f'Admin: {tokenized_path[3].capitalize()}']
            return [tag.capitalize()]
        except IndexError:
            return ["Others"]
        

import json
import logging
import uuid

from django.conf import settings
from django.db import transaction
from django.utils import timezone

from order.models import CheckoutBill, Order, OrderTracking
from wallet.models import Transaction, UserWallet
from wallet.paystack import PaystackClient

client = PaystackClient()


def log_request(*args):
    for arg in args:
        logging.info(arg)


def validate_payment(trans_id):
    with transaction.atomic():
        trans = Transaction.objects.select_for_update().filter(reference=trans_id, status="pending").last()
        if not trans:
            return False, None

        # Get the transaction by the reference number
        payment = client.request_transaction(transaction_reference=trans_id)
        payment_status = payment["paymentStatus"]
        if settings.DEBUG is True:
            payment_status = "PAID"

        if "paymentStatus" in payment and payment_status == "PAID":
            trans.status = "completed"
            trans.amount_paid = float(payment["amountPaid"]) / 100
            trans.save()

        if trans.status == "completed" and trans.transaction_type == "fund_wallet":
            # TopUp user's wallet balance
            wallet = trans.user.userwallet
            wallet.refresh_from_db()
            wallet.balance += trans.amount_paid
            wallet.save()

        if trans.status == "completed" and trans.transaction_type == "order":
            checkout_bill = CheckoutBill.objects.filter(payment_reference=trans_id, payment_mode="bank_transfer", paid_at__isnull=True)
            if checkout_bill.exists():
                checkout_bill.paid_at = timezone.now()
                checkout_bill.save()

                data = {
                    "delivery_address_id": "",
                    "instruction": "",
                    "contact_phone": ""
                }
                meta_data = json.loads(checkout_bill.meta_data) if checkout_bill.meta_data else data

                # Create order
                order = Order.objects.create(
                    user=trans.user,
                    payment=checkout_bill,
                    address=meta_data.get("delivery_address_id"),
                    prep_instruction=meta_data.get("instruction"),
                    contact_phone=meta_data.get("contact_phone")
                )

                # Create order tracking
                tracking = OrderTracking.objects.create(
                    order=order,
                    tracking_id=f"TRK-{order.id.hex[:8].upper()}",
                    status='confirmed'
                )

                description = f"Payment for Order {tracking.tracking_id}"
                trans.narration = description
                trans.save()

            # Notify user
            # notification_message = "Hi, your wallet has been successfully credited with " + str(token_value)
            # create_user_notification(trans.user_id, f"Wallet top-up", notification_message, "fund_wallet")

    return True, trans.return_url


def generate_transaction_reference():
    return str("MACKY-") + str(uuid.uuid4())


def perform_payment_action(request, amount, return_url, transaction_type, description):
    from core.exceptions import InvalidRequestException

    callback_url = request.build_absolute_uri(f'/api/v1/wallet/payment-verify/')
    user = request.user

    wallet, _ = UserWallet.objects.get_or_create(user=user)
    email = user.email
    payment_link = None

    try:

        if not wallet.customer_id:
            customer = client._create_customer(email=email, first_name=user.first_name, last_name=user.last_name)
            new_paystack_customer_id = customer.get('customer_code')
            wallet.customer_id = new_paystack_customer_id
            wallet.save()

        transaction_reference = generate_transaction_reference()

        payment = client.transaction_initialize(email=email, amount=amount, callback_url=callback_url, reference_no=transaction_reference)
        if "authorization_url" in payment:
            payment_link = payment["authorization_url"]

        if payment_link:
            Transaction.objects.create(
                user=user, transaction_type=transaction_type, amount=float(amount), narration=description,
                reference=transaction_reference, return_url=return_url
            )
            return payment_link
        else:
            raise InvalidRequestException({"message": "Error making payment, please try again later"})

    except Exception:
        raise InvalidRequestException({"message": "Error making payment, please try again later"})



from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.utils.serializer_helpers import ReturnDict, ReturnList
from rest_framework import status as drf_status
from rest_framework.exceptions import ErrorDetail

class CustomRenderer(JSONRenderer):
    charset = 'utf-8'

    def render(self, data, accepted_media_type=None, renderer_context=None):
        response = renderer_context.get('response', None)
        status_code = response.status_code if response else 200   
             
        # For successful response
        if status_code == 204 and not data:
            data = {'msg': "deleted successfully!"}

        if drf_status.is_success(status_code) and isinstance(data, (ReturnDict, ReturnList, dict, list)):
            if 'msg' in data:
                message = data.pop('msg')
            elif 'results' in data and 'next' in data and 'previous' in data and isinstance(data['results'], list):
                msg = [result.pop('msg') for result in data['results'] if 'msg' in result]
                if msg and msg[-1]:
                    message = str(msg[-1])
                else:
                    message = f"{renderer_context.get('view').get_view_name()} executed successfully!"
            else:
                message = f"{renderer_context.get('view').get_view_name()} executed successfully!"
            response_data = {
                'status': 'success',
                'message': message,
                'data': data
            }
        else:
            # For error responses
            message = self._convert_errors_to_strings(data)
            response_data = {
                'status': 'error',
                'message':message,
                'data': data if isinstance(data, dict) else None

            }

        return super().render(response_data, accepted_media_type, renderer_context)

    def _convert_errors_to_strings(self, data) -> str:
        if isinstance(data, list) and data:
            return '; '.join([self._convert_errors_to_strings(item) for item in data])
        elif isinstance(data, dict) and data:
            _keys = ('detail', 'non_field_errors')
            for key in _keys:
                if key in data:
                    return self._convert_errors_to_strings(data.get(key))
            resp = {key: self._convert_errors_to_strings(value) for key, value in data.items()}
            message = ''
            for k, v in resp.items():
                message = f"{message}; {k}:{v.strip('.')}"
            return message.strip('; ')
        elif isinstance(data, ErrorDetail):
            return str(data)
        return str(data)



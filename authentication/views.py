from rest_framework import generics, mixins, exceptions, permissions
from django.db.models import Q
from drf_spectacular.utils import extend_schema, OpenApiParameter
from .models import User, OTP_Token, MetaData, IdentityVerification
from foods.models import Kitchen, KitchenImage
from wallet.models import BankInformation
from . import serializers
from .permissions import <PERSON><PERSON><PERSON>, IsChef, IsRider


class CustomerSignupView(generics.CreateAPIView):
    serializer_class = serializers.CustomerSignupSerializer
    permission_classes = []
    authentication_classes = []
    user_type = User.USER_TYPE.USER


class CustomerVerifySignupView(generics.CreateAPIView):
    serializer_class = serializers.CustomerVerifySignupSerializer
    permission_classes = []
    authentication_classes = []


class ChefSignupView(CustomerSignupView):
   user_type = User.USER_TYPE.CHEF


class ChefVerifySignupView(CustomerVerifySignupView):
    pass


class RiderSignupView(CustomerSignupView):
   user_type = User.USER_TYPE.RIDER


class RiderVerifySignupView(CustomerVerifySignupView):
    pass


class SendEmailOTPView(generics.CreateAPIView):
    serializer_class = serializers.SendEmailOTPSerializer
    permission_classes = [permissions.IsAuthenticated]


@extend_schema(
    parameters=[
        OpenApiParameter(name='email', description='user email', required=False, type=str),
        OpenApiParameter(name='phone_number', description='user phone number', required=False, type=str),
    ]
)
class VerifyOTPTokenView(generics.RetrieveAPIView):
    queryset = OTP_Token.objects.select_related('user')
    serializer_class = serializers.VerifyOTPTokenSerializer
    permission_classes=[]
    authentication_classes=[]

    def get_object(self):
        token = self.kwargs.get('token')
        email = self.request.query_params.get('email', None)
        phone_number = self.request.query_params.get('phone_number', None)

        if not (phone_number or email):
            raise exceptions.NotAcceptable("Phone Number or Email must be provided!")
        
        otp = self.queryset.filter(token=token).first()
        if otp is None:
            raise exceptions.ValidationError("Invalid token!")
        return otp



class EmailOTPView(generics.CreateAPIView):
    serializer_class = serializers.EmailOTPSerializer
    permission_classes = []
    authentication_classes = []


class SMSOTPView(generics.CreateAPIView):
    serializer_class = serializers.SMSOTPSerializer
    permission_classes = []
    authentication_classes = []


class CompleteCustomerSignup1View(generics.UpdateAPIView):
    queryset = User.objects.filter(has_completed_kyc=False)
    permission_classes = [IsUser]
    serializer_class = serializers.CompleteCustomerSignup1Serializer
    http_method_names = ['put']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("User User has no pending KYC!")
        return instance

    

class CompleteCustomerSignup2View(generics.UpdateAPIView):
    queryset = User.objects.filter(has_completed_kyc=False)
    serializer_class = serializers.CompleteCustomerSignup2Serializer
    permission_classes = [IsUser]
    http_method_names = ['put']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("User User has no pending KYC!")
        return instance
    

class CompleteChefSignup1View(generics.RetrieveUpdateAPIView):
    queryset = User.objects.filter(has_completed_kyc=False)
    serializer_class = serializers.CompleteChefPersonalInfoSerializer
    permission_classes = [IsChef]
    http_method_names = ['put', 'get']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        return instance
    

class CompleteChefSignup2View(generics.RetrieveUpdateAPIView):
    queryset = Kitchen.objects.all()
    serializer_class = serializers.CompleteChefKitchenInfoSerializer
    permission_classes = [IsChef]
    http_method_names = ['put', 'get']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        if not MetaData.get_user_data(instance, 'chef-kyc-page-1'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        kitchen = self.queryset.filter(chef__user=instance).first()
        if kitchen is None and self.request.method == 'GET':
            raise exceptions.NotFound("User has not updated kitchen")
        return kitchen
    

class CompleteChefSignup3View(generics.RetrieveUpdateAPIView):
    '''Images in the same group should have the same name!'''
    queryset = Kitchen.objects.all()
    serializer_class = serializers.CompleteChefKitchenImageSerializer
    permission_classes = [IsChef]
    http_method_names = ['put', 'get']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        if not MetaData.get_user_data(instance, 'chef-kyc-page-2'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        return self.queryset.get(chef__user=instance)
    

class CompleteChefSignup4View(generics.RetrieveUpdateAPIView):
    queryset = Kitchen.objects.all()
    serializer_class = serializers.CompleteChefKitchenVideoSerializer
    permission_classes = [IsChef]
    http_method_names = ['put', 'get']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        if not MetaData.get_user_data(instance, 'chef-kyc-page-3'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        return self.queryset.get(chef__user=instance)


class CompleteChefSignup5View(generics.RetrieveUpdateAPIView):
    queryset = Kitchen.objects.all()
    serializer_class = serializers.CompleteChefKitchenAddressSerializer
    permission_classes = [IsChef]
    http_method_names = ['put', 'get']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        if not MetaData.get_user_data(instance, 'chef-kyc-page-4'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        return self.queryset.get(chef__user=instance)
    

class CompleteChefSignup6View(generics.RetrieveUpdateAPIView):
    queryset = BankInformation.objects.all()
    serializer_class = serializers.CompleteChefBankInformationSerializer
    permission_classes = [IsChef]
    http_method_names = ['put', 'get']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        if not MetaData.get_user_data(instance, 'chef-kyc-page-5'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        bank_info = self.queryset.filter(user=instance).first()
        if bank_info is None and self.request.method == 'GET':
            raise exceptions.NotFound("User has not updated bank info!")
        return bank_info
    

class CompleteChefSignup7View(generics.RetrieveUpdateAPIView):
    queryset = IdentityVerification.objects.all()
    serializer_class = serializers.CompleteChefIdentityInformationSerializer
    permission_classes = [IsChef]
    http_method_names = ['put', 'get']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        if not MetaData.get_user_data(instance, 'chef-kyc-page-6'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        identity = self.queryset.filter(user=instance).first()
        if identity is None and self.request.method == 'GET':
            raise exceptions.NotFound("User has not updated identity info!")
        return identity
    

class CompleteChefPreviewSignupView(generics.RetrieveAPIView):
    queryset = User.objects.all()
    serializer_class = serializers.CompleteChefPreviewSignupSerializer
    permission_classes = [IsChef]

    def get_object(self):
        instance:User = self.request.user
        if not MetaData.get_user_data(instance, 'chef-kyc-page-7'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        return instance
    

class CompleteChefSubmitSignupView(generics.UpdateAPIView):
    queryset = User.objects.all()
    serializer_class = serializers.CompleteChefSubmitSignupSerializer
    permission_classes = [IsChef]
    http_method_names = ['put']

    def get_object(self):
        instance:User = self.request.user
        if instance.has_completed_kyc:
            raise exceptions.NotFound("Chef User has no pending KYC!")
        if not MetaData.get_user_data(instance, 'chef-kyc-page-7'):
           raise exceptions.ValidationError("Previous page has not been updated!")
        return instance


class ForgotPasswordView(generics.UpdateAPIView):
    queryset = User.objects.filter(is_active=True)
    serializer_class = serializers.ForgotPasswordSerializer
    permission_classes = []
    authentication_classes = []
    http_method_names = ['put']

    def get_object(self):
        email = self.request.data.get('email', None)
        phone_number = self.request.data.get('phone_number', None)
        if not (phone_number or email):
            raise exceptions.ValidationError("Phone Number or Email must be provided!")
        try:
            if email:
                user = User.objects.get(email=email)
            else:
                user = User.objects.get(phone_number=phone_number)
        except User.DoesNotExist:
            raise exceptions.NotFound("No user with the credentials found!")
        return user


from django.urls import path, include
from . import views
from rest_framework_simplejwt.views import (TokenObtainPairView, TokenRefreshView, TokenVerifyView)

urlpatterns = [
    path('chef/signup/', views.ChefSignupView.as_view(), name='chef-signup'),
    path('chef/signup/verify-phone-number/', views.ChefVerifySignupView.as_view(), name='chef-signup-verify'),
    path('chef/signup/complete-profile/personal-information/', views.CompleteChefSignup1View.as_view(), name='complete-chef-signup-1'),
    path('chef/signup/complete-profile/kitchen-information/', views.CompleteChefSignup2View.as_view(), name='complete-chef-signup-2'),
    path('chef/signup/complete-profile/kitchen-images/', views.CompleteChefSignup3View.as_view(), name='complete-chef-signup-3'),
    path('chef/signup/complete-profile/kitchen-videos/', views.CompleteChefSignup4View.as_view(), name='complete-chef-signup-4'),
    path('chef/signup/complete-profile/kitchen-address/', views.CompleteChefSignup5View.as_view(), name='complete-chef-signup-5'),
    path('chef/signup/complete-profile/bank-information/', views.CompleteChefSignup6View.as_view(), name='complete-chef-signup-6'),
    path('chef/signup/complete-profile/identity-verification/', views.CompleteChefSignup7View.as_view(), name='complete-chef-signup-7'),
    path('chef/signup/complete-profile/preview/', views.CompleteChefPreviewSignupView.as_view(), name='complete-chef-signup-preview'),
    path('chef/signup/complete-profile/submit/', views.CompleteChefSubmitSignupView.as_view(), name='complete-chef-signup-submit'),
    
    path('rider/signup/', views.RiderSignupView.as_view(), name='rider-signup'),
    path('rider/signup/verify-phone-number/', views.RiderVerifySignupView.as_view(), name='rider-signup-verify'),

    path('customer/signup/', views.CustomerSignupView.as_view(), name='customer-signup'),
    path('customer/signup/verify-phone-number/', views.CustomerVerifySignupView.as_view(), name='customer-signup'),
    path('customer/signup/complete-profile/', views.CompleteCustomerSignup1View.as_view(), name='complete-customer-signup-1'),
    path('customer/signup/complete-profile/send-email-otp/', views.SendEmailOTPView.as_view(), name='send-email-otp'),
    path('customer/signup/complete-profile/update-address/', views.CompleteCustomerSignup2View.as_view(), name='complete-customer-signup-2'),

    path('user/otp/<str:token>/verify-token-validity/', views.VerifyOTPTokenView.as_view(), name='verify-otp'),

    path('user/send-otp/email/', views.EmailOTPView.as_view(), name='email-otp'),
    path('user/send-otp/sms/', views.SMSOTPView.as_view(), name='sms-otp'),

    path('user/forgot-password/', views.ForgotPasswordView.as_view(), name='forgot-password'),

    path('user/sign-in/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('user/sign-in/refresh-token/', TokenRefreshView.as_view(), name='token_refresh'),
    path('user/sign-in/verify-token/', TokenVerifyView.as_view(), name='token_verify'),
]
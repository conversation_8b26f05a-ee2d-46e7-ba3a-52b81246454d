from rest_framework import permissions


class IsChef(permissions.BasePermission):
    '''
    Custom permission class allowing access to only Chef's.
    '''

    message = 'You are not a Chef!'

    def has_permission(self, request, view):
        return (request.user and request.user.is_authenticated and request.user.user_type == request.user.USER_TYPE.CHEF)
    

class IsAdmin(permissions.BasePermission):
    '''
    Custom permission class allowing access to only Admin's.
    '''

    message = 'You are not an Admin!'

    def has_permission(self, request, view):
        return (request.user and request.user.is_authenticated and request.user.user_type == request.user.USER_TYPE.ADMIN)
    

class IsUser(permissions.BasePermission):
    '''
    Custom permission class allowing access to only User's.
    '''

    message = 'You are not a user!'

    def has_permission(self, request, view):
        return (request.user and request.user.is_authenticated and request.user.user_type == request.user.USER_TYPE.USER)
    

class IsRider(permissions.BasePermission):
    '''
    Custom permission class allowing access to only Rider's.
    '''

    message = 'You are not a rider!'

    def has_permission(self, request, view):
        return (request.user and request.user.is_authenticated and request.user.user_type == request.user.USER_TYPE.RIDER)


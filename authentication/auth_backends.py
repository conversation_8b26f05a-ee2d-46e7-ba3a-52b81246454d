from django.contrib.auth.backends import AllowAllUsersModelBackend
from django.contrib.auth import get_user_model
from django.conf import settings
from phonenumber_field.phonenumber import to_python

User = get_user_model()

class EmailOrPhoneBackend(AllowAllUsersModelBackend):
    """
    Custom authentication backend that allows users to log in with either email or phone number.
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        user = None
        
        if username:
            if "@" in username:
                user = User.objects.filter(email=username).first()
            else:
                try:
                    username = to_python(username, settings.PHONENUMBER_DEFAULT_REGION)
                except Exception as e:
                    print(e)
                user = User.objects.filter(phone_number=username).first()

        if user and user.check_password(password):
            return user
        
        return None 
# Generated by Django 5.1.7 on 2025-05-04 18:24

import authentication.models
import django.db.models.deletion
import phonenumber_field.modelfields
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='user',
            name='username',
        ),
        migrations.AddField(
            model_name='user',
            name='address',
            field=models.TextField(blank=True, default=None, null=True, verbose_name='Address'),
        ),
        migrations.AddField(
            model_name='user',
            name='birth_date',
            field=models.DateField(default=None, null=True, verbose_name='Birth Date'),
        ),
        migrations.AddField(
            model_name='user',
            name='has_completed_kyc',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='latitude',
            field=models.DecimalField(decimal_places=7, default=None, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='longitude',
            field=models.DecimalField(decimal_places=7, default=None, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='phone_number',
            field=phonenumber_field.modelfields.PhoneNumberField(blank=True, db_index=True, default=None, max_length=128, null=True, region=None, unique=True, verbose_name='Phone Number'),
        ),
        migrations.AddField(
            model_name='user',
            name='user_type',
            field=models.CharField(choices=[('admin', 'Admin'), ('user', 'User'), ('chef', 'Chef'), ('rider', 'Rider')], default='user', max_length=10),
        ),
        migrations.AlterField(
            model_name='user',
            name='email',
            field=models.EmailField(blank=True, db_index=True, default=None, max_length=254, null=True, unique=True, verbose_name='email address'),
        ),
        migrations.CreateModel(
            name='IdentityVerification',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('country', models.CharField(max_length=100)),
                ('document_type', models.CharField(max_length=50)),
                ('document', models.FileField(upload_to='verification/identity/')),
                ('verified', models.BooleanField(default=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='user_verification', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MetaData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('data', models.JSONField(default=dict)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='user_meta_data', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='OTP_Token',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(default=authentication.models._gen_code, editable=False, max_length=10)),
                ('email', models.EmailField(blank=True, default=None, max_length=254, null=True)),
                ('phone_number', phonenumber_field.modelfields.PhoneNumberField(blank=True, default=None, max_length=128, null=True, region=None)),
                ('expiration', models.DateTimeField()),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('modified', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='verification_token', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]

from django.db import models, transaction
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.utils.translation import gettext_lazy as _
from rest_framework import exceptions
from django.conf import settings
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from phonenumber_field.modelfields import PhoneNumberField
from uuid import uuid4, UUID
from resend import Emails
import random, string, secrets

class UserManager(BaseUserManager):
    """
    Custom user model manager where email is the unique identifiers
    for authentication instead of usernames.
    """

    def create_user(self, password, email="", phone_number="", **extra_fields):
        """
        Create and save a User with the given email/phone_number and password.
        """
        if email:
            extra_fields['email'] = self.normalize_email(email)
        if phone_number:
            extra_fields['phone_number'] = phone_number
        user: User = self.model(**extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, password, **extra_fields):
        """
        Create and save a SuperUser with the given email and password.
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        extra_fields.setdefault('user_type', User.USER_TYPE.ADMIN)

        if extra_fields.get('is_staff') is not True:
            raise ValueError(_('Superuser must have is_staff=True.'))
        if extra_fields.get('is_superuser') is not True:
            raise ValueError(_('Superuser must have is_superuser=True.'))
        return self.create_user(email=email, password=password, **extra_fields)


class User(AbstractUser):
    """
    A base class implementing a fully featured User model with
    admin-compliant permissions.

    Email and password are required. Other fields are optional.
    """
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    username = None #models.CharField(max_length=150, blank=True, null=True)
    email = models.EmailField(_('email address'), null=True, blank=True, unique=True, default=None, db_index=True)
    phone_number = PhoneNumberField(_('Phone Number'), null=True, blank=True, unique=True, default=None, db_index=True)
    address = models.TextField(_('Address'), null=True, blank=True, default=None)
    birth_date = models.DateField(_('Birth Date'), null=True, default=None)

    class USER_TYPE(models.TextChoices):
        ADMIN = 'admin', _('Admin')
        USER = 'user', _('User')
        CHEF = 'chef', _('Chef')
        RIDER = 'rider', _('Rider')
    user_type = models.CharField(max_length=10, choices=USER_TYPE.choices, default=USER_TYPE.USER)
    has_completed_kyc = models.BooleanField(default=False)

    latitude = models.DecimalField(max_digits=10, decimal_places=7, null=True, default=None)
    longitude = models.DecimalField(max_digits=10, decimal_places=7, null=True, default=None)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self):
        return str(self.email or self.phone_number or "N/A")

    def email_user(self, subject:str, message:str="", html:str="", from_email:str = "MackysFood <<EMAIL>>", ):
        if not self.email:
            raise exceptions.NotFound("Email not found!")
        params:Emails.SendParams = {
            "from": from_email,
            "to": [self.email],
            "subject": subject,
            "html": html,
            "text": message,
        }
        return Emails.send(params)
    
    def sms_user(self, subject:str, message:str="", *args, **kwargs):
        if not self.phone_number:
            raise exceptions.NotFound("Phone Number not found!")
        
        emails = ['<EMAIL>']
        if self.email:
            emails.append(self.email)
        
        subject = f'{subject}: SMS TO {self.phone_number}'
        params:Emails.SendParams = {
            "from": "SMS <<EMAIL>>",
            "to": emails,
            "subject": subject,
            "text": message,
        }
        return Emails.send(params)

    def clean(self):
        if not self.email and not self.phone_number:
            raise exceptions.ValidationError("Either email or phone number must be provided.")


def _gen_code(cls=None, length=settings.OTP_CODE_LEN, retries=5):
    if not (4 <= length <= 10):
        raise ValueError("OTP length must be between 4 and 10 digits.")
    
    for _ in range(retries):
        min_val = 10**(length - 1)
        max_val = 10**length - 1
        code = str(secrets.randbelow(max_val - min_val + 1) + min_val)

        if cls is None:
            return code
        if not cls.objects.filter(token=code).exists():
            return code
    raise Exception("Failed to generate a unique OTP token after multiple attempts")

class OTP_Token(models.Model):
    token = models.CharField(max_length=10, default=_gen_code, editable=False)
    user = models.OneToOneField(User, related_name='verification_token', on_delete=models.CASCADE)
    email = models.EmailField(null=True, blank=True, default=None)
    phone_number = PhoneNumberField(null=True, blank=True, default=None)
    expiration = models.DateTimeField()
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def is_valid(self):
        if not self.expiration: 
            return True
        return timezone.now() < self.expiration
    
    @classmethod
    def _generate_token_obj(cls, user, save_email, save_phone_number):
        kwargs = {}
        if isinstance(user, User):
            kwargs['user'] = user
        else:
            kwargs['user_id'] = user

        otp = cls.objects.filter(**kwargs).first()
        if otp:
            otp.expiration = (timezone.now() + timedelta(minutes=settings.OTP_EXPIRY_MINUTES))
            otp.email = save_email
            otp.phone_number = save_phone_number
            otp.token = _gen_code(cls)
            otp.save()
        else:
            otp = cls.objects.create(
                expiration=(timezone.now() + timedelta(minutes=settings.OTP_EXPIRY_MINUTES)),
                email=save_email,
                phone_number = save_phone_number,
                token=_gen_code(cls),
                **kwargs
            )
        return otp
    
    @classmethod
    def generate_token(cls, user:User|UUID, save_email:str=None, save_phone_number:str=None)->str:
        return  cls._generate_token_obj(user, save_email, save_phone_number).token
    
    @classmethod
    def verify_token_user(cls, token:str, saved_email:str=None, save_phone_number:str=None):
        try:
            obj = cls.objects.select_related('user').get(token=token, email=saved_email, phone_number=save_phone_number)
            return obj.user, obj.is_valid()
        except cls.DoesNotExist:
            return None, False
        
    
    @classmethod
    def verify_token_obj(cls, token:str, saved_email:str=None, save_phone_number:str=None):
        try:
            obj = cls.objects.select_related('user').get(token=token, email=saved_email, phone_number=save_phone_number)
            return obj, obj.is_valid()
        except cls.DoesNotExist:
            return None, False
            
    
    @classmethod
    def verify_token(cls, token:str, saved_email:str=None, save_phone_number:str=None)->bool:
        try:
            obj = cls.objects.get(token=token, email=saved_email, phone_number=save_phone_number)
            return obj.is_valid()
        except cls.DoesNotExist:
            return False
        
    @classmethod
    def verify_token_once(cls, token:str, saved_email:str=None, save_phone_number:str=None)->bool:
        try:
            obj = cls.objects.get(token=token, email=saved_email, phone_number=save_phone_number)
            verified = obj.is_valid()
            if verified:
                obj.delete()
            return verified
        except cls.DoesNotExist:
            return False
    
    def __str__(self) -> str:
        return f"{self.token} : {self.user.email}"


class IdentityVerification(models.Model):
    id = models.UUIDField(primary_key=True, editable=False, default=uuid4)
    user = models.OneToOneField(User, related_name='user_verification', on_delete=models.CASCADE)
    country = models.CharField(max_length=100)
    class DOC_TYPE(models.TextChoices):
        NIN = ('nin', 'NIN')
        VOTERS_CARD = ('voters_card', 'Voters Card')
        PASSPORT = ("passport", "Passport")
    document_type = models.CharField(max_length=50, choices=DOC_TYPE.choices)
    document = models.FileField(upload_to='verification/identity/')
    verified = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.user} {self.verified}"
    


class MetaData(models.Model):
    user = models.OneToOneField(User, related_name='user_meta_data', on_delete=models.CASCADE)
    data = models.JSONField(default=dict)
    created = models.DateTimeField(auto_now_add=True)
    modified = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return f"{self.user}"
    
    @classmethod
    def get_user_data(cls, user:User, key:str):
        obj, _ = cls.objects.get_or_create(user=user)
        return obj.data.get(key, None)

    @classmethod
    def update_user_data(cls, user:User, key:str, data):
        with transaction.atomic():
            obj, _ = cls.objects.get_or_create(user=user)
            if obj.data.get(key, None) != data:
                obj.data[key] = data
                obj.save()
        return True
    



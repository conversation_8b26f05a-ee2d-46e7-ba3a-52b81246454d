from rest_framework import serializers, exceptions
from django.db import transaction
from django.conf import settings
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from phonenumber_field.serializerfields import PhoneNumberField
from django.contrib.auth.models import update_last_login
from django.utils import timezone

from .models import User, OTP_Token, MetaData, IdentityVerification
from .auth_backends import EmailOrPhoneBackend
from user_profiles.models import ChefUser
from wallet.models import Bank, BankInformation
from wallet.paystack import PaystackClient
from foods.models import Kitchen, KitchenImage, KitchenImageGroup


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    username_field='identifier'

    def validate(self, data):
        authenticate_kwargs = {
            "username": data.get("identifier"),
            "password": data.get("password"),
            "request": self.context["request"],
        }
        
        self.user = user = EmailOrPhoneBackend().authenticate(**authenticate_kwargs)

        if user is None:
            raise exceptions.AuthenticationFailed("Invalid credentials")

        if not user.is_active:
            raise exceptions.AuthenticationFailed("Your account is disabled")
        
        refresh = self.get_token(user)

        if getattr(settings, 'UPDATE_LAST_LOGIN', None):
            update_last_login(None, user)

        return {
            "refresh_token": str(refresh),
            "access_token": str(refresh.access_token),
            "user": {
                "id": user.id,
                "user_type": user.user_type,
                "has_completed_kyc": user.has_completed_kyc,
                "email": user.email,
                "phone_number": str(user.phone_number),
                "first_name": user.first_name,
                "last_name": user.last_name,
            },
            "msg": "signin successful!",
        }
    

class CustomerSignupSerializer(serializers.ModelSerializer):
    # otp_code = serializers.CharField(max_length=10, write_only=True)
    phone_number = PhoneNumberField(required=True)
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Signup successful! Check your mail for otp!"

    class Meta:
            model = User
            fields = ('phone_number', 'password', 'msg')
            extra_kwargs = {
                'password': {'write_only': True,}
            }

    def create(self, validated_data):
        password = validated_data.pop('password')
        phone_number = validated_data.pop('phone_number')
        # otp_code = validated_data.pop('otp_code')

        # if OTP_Token.verify_token_once(otp_code, save_phone_number=phone_number) is False:
        #     raise serializers.ValidationError("Invalid token!")

        validated_data['is_active'] = False
        validated_data['user_type'] = self.context['view'].user_type
        with transaction.atomic():
            user, created = User.objects.get_or_create(phone_number=phone_number, defaults=validated_data)
            if user.is_active:
                raise exceptions.ValidationError("User with this phone number already exists.")
            user.set_password(password)
            user.save()
        
        otp_code = OTP_Token.generate_token(user, save_phone_number=phone_number)
        try:
            user.sms_user(
                "OTP Verification",
                f"Hello,\n\nYour OTP for verification is {otp_code}.\n\nThank you.",
            )
        except Exception as e:
            raise exceptions.ValidationError(f"Failed to send OTP: {str(e)}")

        return user
    

class CustomerVerifySignupSerializer(serializers.ModelSerializer):
    otp_code = serializers.CharField(max_length=10, write_only=True)
    phone_number = PhoneNumberField(required=True)
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Account verified successful!"

    class Meta:
            model = User
            fields = ('phone_number', 'otp_code', 'msg')

    def create(self, validated_data):
        phone_number = validated_data.pop('phone_number')
        otp_code = validated_data.pop('otp_code')

        with transaction.atomic():
            token, valid = OTP_Token.verify_token_obj(otp_code, save_phone_number=phone_number)
            if not valid:
                raise serializers.ValidationError("Invalid token!")
            user = token.user
            user.is_active = True
            user.save()
            token.delete()
        return user
    


class SendEmailOTPSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "OTP sent successfully!"

    class Meta:
        model = User
        fields = ('email', 'msg')

    def create(self, validated_data):
        email = validated_data['email']
        user:User = self.context['request'].user
        otp_code = OTP_Token.generate_token(user, email)
        user.email = email
        try:
            user.email_user(
                "OTP Verification",
                f"Hello,\n\nYour OTP for KYC verification is {otp_code}.\n\nThank you.",
            )
        except Exception as e:
            raise exceptions.ValidationError(f"Failed to send OTP: {str(e)}")
        return user


class VerifyOTPTokenSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj:OTP_Token)->str:
        email = self.context['request'].query_params.get('email', None)
        phone_number = self.context['request'].query_params.get('phone_number', None)
        if not (obj.email == email or obj.phone_number == phone_number or obj.user.email == email or obj.user.phone_number == phone_number):
            raise exceptions.ValidationError("Invalid token!")
        if not obj.is_valid():
            raise exceptions.ValidationError(f"Expired {(timezone.now()-obj.expiration).seconds/60} minutes ago!")
        return f"Valid token! Expires in {(obj.expiration-timezone.now()).seconds/60} minutes!"
    class Meta:
        model = OTP_Token
        fields = ['token', 'msg']


class EmailOTPSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()
    email = serializers.EmailField()

    def get_msg(self, obj)->str:
        return "OTP sent successfully!"

    class Meta:
        model = User
        fields = ('email', 'msg')

    def create(self, validated_data):
        email = validated_data['email']
        user = User.objects.filter(email=email).first()
        if not user:
            raise exceptions.NotFound("User with email not found!")
        otp_code = OTP_Token.generate_token(user, save_email=email)
        try:
            user.email_user(
                "OTP Verification",
                f"Hello,\n\nYour OTP for verification is {otp_code}.\n\nThank you.",
            )
        except Exception as e:
            raise exceptions.ValidationError(f"Failed to send OTP: {str(e)}")
        return user
    

class SMSOTPSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()
    phone_number = PhoneNumberField()

    def get_msg(self, obj)->str:
        return "OTP sent successfully!"

    class Meta:
        model = User
        fields = ('phone_number', 'msg')

    def create(self, validated_data):
        phone_number = validated_data['phone_number']
        user = User.objects.filter(phone_number=phone_number).first()
        if not user:
            raise exceptions.NotFound("User with phone number not found!")
        otp_code = OTP_Token.generate_token(user, save_phone_number=phone_number)
        try:
            user.sms_user(
                "OTP Verification",
                f"Hello,\n\nYour OTP for verification is {otp_code}.\n\nThank you.",
            )
        except Exception as e:
            raise exceptions.ValidationError(f"Failed to send OTP: {str(e)}")
        return user


class CompleteCustomerSignup1Serializer(serializers.ModelSerializer):
    otp_code = serializers.CharField(max_length=10, write_only=True)
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        if obj.email:
            return 'Already updated!'
        return "Profile updated!"
    
    class Meta:
        model = User
        fields = ('id', 'email', 'phone_number', 'first_name', 'last_name', 'birth_date', 'otp_code', 'msg')
        read_only_fields = ('id', 'phone_number')
        extra_kwargs = {
            'first_name': {'allow_null': False},
            'last_name': {'allow_null': False},
            'birth_date': {'allow_null': False},
        }
    
    def validate(self, attrs):
        if self.context['request'].user.has_completed_kyc:
            raise exceptions.ValidationError("User already has completed KYC")
        return super().validate(attrs)

    def update(self, instance:User, validated_data):
        if instance.email:
            return instance
        if instance.address:
            validated_data['has_completed_kyc'] = True
        otp = validated_data.pop('otp_code')
        email = validated_data.get('email')

        if OTP_Token.verify_token_once(otp, email) is False:
            raise exceptions.ValidationError("Invalid OTP code")
        return super().update(instance, validated_data)
    

class CompleteCustomerSignup2Serializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Profile updated!"
    
    class Meta:
        model = User
        fields = ('id', 'address', 'longitude', 'latitude', 'msg')
        read_only_fields = ('id',)
        extra_kwargs = {
            'address': {'allow_null': False},
            'longitude': {'allow_null': False},
            'latitude': {'allow_null': False},
        }
    
    def update(self, instance:User, validated_data):
        if instance.email:
            validated_data['has_completed_kyc'] = True
        return super().update(instance, validated_data)
    

class ChefCompleteProfileSerializer(serializers.ModelSerializer):
    special_services = serializers.SerializerMethodField()

    def get_special_services(self, obj:ChefUser)->list[str]:
        return [serivice.name for serivice in obj.special_service.all()]
    
    class Meta:
        model = ChefUser
        fields = ['display_image', 'culnary_experience', 'prof_accreditation', 'prof_certificate', 'years_cooking', 'school_attended', 'special_service', 'special_services', 'other_special_services', 'about']
        extra_kwargs = {
            'special_service': {'write_only': True},
        }


class CompleteChefPersonalInfoSerializer(serializers.ModelSerializer):
    chef = ChefCompleteProfileSerializer()
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Persoal profile updated!"

    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'phone_number', 'email', 'chef', 'msg']
        read_only_fields = ['id', 'phone_number']

    def update(self, instance:User, validated_data:dict):
        chef_data = validated_data.pop('chef', {})
        special_service = chef_data.pop('special_service', [])
        try:
            with transaction.atomic():
                chef, _ = ChefUser.objects.update_or_create(user=instance, defaults=chef_data)
                chef.special_service.set(special_service)
                chef.save()
                for k, v in validated_data.items():
                    setattr(instance, k, v)
                instance.save()
                MetaData.update_user_data(instance, 'chef-kyc-page-1', True)
        except Exception as e:
            raise serializers.ValidationError(e)
        return instance
    

class CompleteChefKitchenInfoSerializer(serializers.ModelSerializer):
    amenities_name = serializers.SerializerMethodField()
    msg = serializers.SerializerMethodField()

    def get_amenities_name(self, obj:Kitchen)->list[str]:
        return [amenity.name for amenity in obj.amenities.all()]

    def get_msg(self, obj)->str:
        return "Kitchen info updated!"
    
    class Meta:
        model = Kitchen
        fields = ['cover_image', 'type', 'amenities', 'amenities_name', 'msg']
        extra_kwargs = {
            'amenities': {'required': True, "write_only":True}
        }

    def update(self, instance:Kitchen, validated_data):
        amenities = validated_data.pop('amenities', [])
        try:
            with transaction.atomic():
                for k, v in validated_data.items():
                    setattr(instance, k, v)
                instance.amenities.set(amenities)
                instance.save()
                MetaData.update_user_data(self.context['request'].user, 'chef-kyc-page-2', True)
        except Exception as e:
            raise serializers.ValidationError(e)
        return instance
    
    def create(self, validated_data):
        user = self.context['request'].user
        chef = user.chef
        amenities = validated_data.pop('amenities')
        try:
            with transaction.atomic():
                kitchen = Kitchen.objects.create(chef=chef, **validated_data)
                kitchen.amenities.set(amenities)
                kitchen.save()
                MetaData.update_user_data(user, 'chef-kyc-page-2', True)
        except Exception as e:
            raise serializers.ValidationError(e)
        return kitchen


class ChefKitchenImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = KitchenImage
        fields = ['image'] 


class KitchenImageGroupSerializer(serializers.ModelSerializer):
    images = ChefKitchenImageSerializer(many=True, required=True)
    class Meta:
        model = KitchenImageGroup
        fields = ['name', 'images']    


class CompleteChefKitchenImageSerializer(serializers.ModelSerializer):
    image_groups = KitchenImageGroupSerializer(many=True, required=True)
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Kitchen images updated!"

    class Meta:
        model = Kitchen
        fields = ['image_groups', 'msg']

    def update(self, instance:Kitchen, validated_data):
        image_groups = validated_data.pop('image_groups', [])
        with transaction.atomic():
            for group in image_groups:
                group_obj = KitchenImageGroup.objects.filter(kitchen=instance, name=group.get('name', None)).first()
                if group_obj is None:
                    group_obj = KitchenImageGroup.objects.create(kitchen=instance, name=group.get('name', None))
                else:
                    group_obj.images.delete()
                KitchenImage.objects.bulk_create(
                    [KitchenImage(group=group_obj, image=obj['image']) for obj in group.get('images', [])]
                )
            MetaData.update_user_data(self.context['request'].user, 'chef-kyc-page-3', True)
        return instance


class CompleteChefKitchenVideoSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Kitchen video updated!"
    
    class Meta:
        model = Kitchen
        fields = ['video', 'msg']
        extra_kwargs = {
            'video': {'required': True}
        }

    def update(self, instance, validated_data):
        with transaction.atomic():
            MetaData.update_user_data(self.context['request'].user, 'chef-kyc-page-4', True)
            instance = super().update(instance, validated_data)
        return instance


class CompleteChefKitchenAddressSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Kitchen address updated!"
    
    class Meta:
        model = Kitchen
        fields = ['address', 'msg']
        extra_kwargs = {
            'address': {'required': True}
        }

    def update(self, instance, validated_data):
        with transaction.atomic():
            MetaData.update_user_data(self.context['request'].user, 'chef-kyc-page-5', True)
            instance = super().update(instance, validated_data)
        return instance


class CompleteChefBankInformationSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()
    bank_code = serializers.CharField(write_only=True)
    bank_name = serializers.SerializerMethodField()

    def get_bank_name(self, obj:BankInformation)->str:
        return obj.bank.name

    def get_msg(self, obj)->str:
        return "Bank information updated!"
    
    class Meta:
        model = BankInformation
        fields = ['account_number', 'account_name', 'bank_name', 'bank_code', 'msg']
        extra_kwargs = {
            'account_name': {'read_only': True}
        }

    def create(self, validated_data):
        account_number = validated_data.pop('account_number')
        bank_code = validated_data.pop('bank_code')
        user = self.context['request'].user
        bank = Bank.objects.filter(code=bank_code).first()
        
        if bank is None:
            if not Bank.objects.exists():
                raise serializers.ValidationError("Bank list have not been generated")
            raise serializers.ValidationError("Bank not found!")
        try:
            resp = PaystackClient().disburstment_validate_account(account_number, bank_code)
            with transaction.atomic():
                instance = BankInformation.objects.create(user=user, bank=bank, account_number=account_number, account_name=resp['account_name'])
                MetaData.update_user_data(user, 'chef-kyc-page-6', True)
        except Exception as e:
            raise serializers.ValidationError(e)
        return instance
    
    def update(self, instance:BankInformation, validated_data):
        account_number = validated_data.get('account_number')
        bank_code = validated_data.get('bank_code')
        bank = Bank.objects.filter(code=bank_code).first()
        
        if bank is None:
            if not Bank.objects.exists():
                raise serializers.ValidationError("Bank list have not been generated")
            raise serializers.ValidationError("Bank not found!")
        try:
            resp = PaystackClient().disburstment_validate_account(account_number, bank_code)
            with transaction.atomic():
                instance.bank = bank
                instance.account_number=account_number
                instance.account_name=resp['account_name']
                instance.save()
                MetaData.update_user_data(self.context['request'].user, 'chef-kyc-page-6', True)
        except Exception as e:
            raise serializers.ValidationError(e)
        return instance
    

class CompleteChefIdentityInformationSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "Identity information updated!"
    
    class Meta:
        model = IdentityVerification
        fields = ['country', 'document_type', 'document', 'msg']

    def create(self, validated_data):
        country = validated_data.pop('country')
        document_type = validated_data.pop('document_type')
        document = validated_data.pop('document')
        user = self.context['request'].user
        
        try:
            with transaction.atomic():
                instance = IdentityVerification.objects.create(user=user, country=country, document_type=document_type, document=document)
                MetaData.update_user_data(user, 'chef-kyc-page-7', True)
        except Exception as e:
            raise serializers.ValidationError(e)
        return instance
    
    def update(self, instance:IdentityVerification, validated_data):
        country = validated_data.pop('country')
        document_type = validated_data.pop('document_type')
        document = validated_data.pop('document')
        
        try:
            with transaction.atomic():
                instance.country = country
                instance.document_type=document_type
                instance.document=document
                instance.save()
                MetaData.update_user_data(self.context['request'].user, 'chef-kyc-page-7', True)
        except Exception as e:
            raise serializers.ValidationError(e)
        return instance
    

class PreviewKitchenSignupSerializer(serializers.ModelSerializer):
    image_groups = KitchenImageGroupSerializer(many=True)
    amenities_name = serializers.SerializerMethodField()

    def get_amenities_name(self, obj:Kitchen)->list[str]:
        return [amenity.name for amenity in obj.amenities.all()]
    
    class Meta:
        model = Kitchen
        fields = ['cover_image', 'type', 'amenities', 'amenities_name', 'image_groups', 'video', 'address']
        extra_kwargs = {
            'amenities': {'write_only': True},
        }
    

class CompleteChefPreviewSignupSerializer(serializers.ModelSerializer):
    chef = ChefCompleteProfileSerializer()
    kitchen = PreviewKitchenSignupSerializer(source='chef.kitchen')
    bank_info = CompleteChefBankInformationSerializer()
    user_verification = CompleteChefIdentityInformationSerializer()
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "fetched preview!"
    
    def to_representation(self, instance):
        data = super().to_representation(instance)
        data.get('bank_info', {}).pop('msg', None)
        data.get('user_verification', {}).pop('msg', None)
        return data
    class Meta:
        model = User
        fields = ['id', 'first_name', 'last_name', 'phone_number', 'email', 'chef', 'kitchen', 'bank_info', 'user_verification', 'msg']


class CompleteChefSubmitSignupSerializer(serializers.ModelSerializer):
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj)->str:
        return "KYC submitted successfully!"

    class Meta:
        model = User
        fields = ['msg']

    def update(self, instance:User, validated_data):
        instance.has_completed_kyc = True
        instance.save()
        return instance
    

class ForgotPasswordSerializer(serializers.ModelSerializer):
    token = serializers.CharField(write_only=True)
    email = serializers.EmailField(required=False)
    phone_number = PhoneNumberField(required=False)
    msg = serializers.SerializerMethodField()
    
    def get_msg(self, obj)->str:
        return "password updated successfully!"
    
    class Meta:
        model = User
        fields = ['phone_number', 'email', 'password', 'token', 'msg']
        extra_kwargs = {
            'password': {"write_only": True}
        }

    def update(self, user:User, validated_data):
        phone_number = validated_data.pop("phone_number", None)
        email = validated_data.pop("email", None)
        token = validated_data.pop('token')
        password = validated_data.pop('password')
        if (email and OTP_Token.verify_token_once(token, saved_email=email)) or (phone_number and (OTP_Token.verify_token_once(token, save_phone_number=phone_number))):
            user.set_password(password)
            user.save()
        else:
            raise exceptions.ValidationError("Invalid OTP!")
        return user



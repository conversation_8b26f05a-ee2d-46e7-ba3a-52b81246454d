from uuid import uuid4

from django.db import models
from authentication.models import User


class DispatchVehicleChoices(models.TextChoices):
    BUS = ("bus", "Bus")
    MOTORCYCLE = ("motorcycle", "Motorcycle")
    CAR = ("car", "Car")
    TRICYCLE = ("tricycle", "Tricycle")
    BICYCLE = ("bicycle", "Bicycle")
    TRUCK = ("truck", "Truck")


class DispatchDriver(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=False)
    vehicle_type = models.CharField(max_length=100, choices=DispatchVehicleChoices.choices, default="bicycle")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.id}: {self.user.email}"




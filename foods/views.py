from rest_framework import generics, exceptions, permissions, viewsets
from authentication.permissions import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsUser
from django.db.models import Prefetch, Case, When, IntegerField
from django_filters.rest_framework import DjangoFilterBackend
from . import serializers
from .models import KitchenAmenity, Kitchen, Food, FoodRating, KitchenImageGroup, KitchenImage
from user_profiles.models import ChefUser
from .filters import MarketPlaceFilter, FoodListFilter


class GetMarketPlaceFoodView(generics.ListAPIView):
    queryset =  (
        Kitchen.objects.select_related('chef', 'chef__user')
        .prefetch_related(Prefetch('foods', queryset=Food.objects.all()[:3]))
        .order_by('-created_at')
        )
    filter_backends = [DjangoFilterBackend]
    filterset_class = MarketPlaceFilter
    permission_classes = [IsUser]
    serializer_class = serializers.GetUserMarketPlaceKitchenSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['food_filters'] = self.request.query_params
        return context


class GetChefKitchenFoodView(generics.ListAPIView):
    queryset = Kitchen.objects.select_related('chef', 'chef__user').order_by('-created_at')
    permission_classes = [IsUser]
    serializer_class = serializers.GetChefKitchenSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_class = MarketPlaceFilter


class GetKitchenAmenitiesListView(generics.ListAPIView):
    queryset = KitchenAmenity.objects.all()
    permission_classes = [permissions.IsAuthenticated]
    serializer_class = serializers.GetChefKitchenAmenitiesListSerializer
    lookup_url_kwarg = "kitchen_id"

    def get_queryset(self):
        kitchen_id = self.kwargs[self.lookup_url_kwarg]
        return self.queryset.filter(kitchens__id=kitchen_id).distinct()


class FoodListView(generics.ListAPIView):
    queryset = Food.objects.all()
    serializer_class = serializers.RetrieveFoodWithListSerializer
    permission_classes = [IsUser]
    filter_backends = [DjangoFilterBackend]
    filterset_class = FoodListFilter
    lookup_url_kwarg = "kitchen_id"

    def get_queryset(self):
        kitchen_id = self.kwargs[self.lookup_url_kwarg]
        return self.queryset.filter(kitchen_id=kitchen_id)
    

class KitchenImageListView(generics.ListAPIView):
    queryset = KitchenImageGroup.objects.prefetch_related('images')
    serializer_class = serializers.GetKitchenImageGroupSerializer
    permission_classes = [IsUser]
    lookup_url_kwarg = "kitchen_id"

    def get_queryset(self):
        kitchen_id = self.kwargs[self.lookup_url_kwarg]
        return self.queryset.filter(kitchen__id=kitchen_id)


class ReviewRatingViewSet(viewsets.ModelViewSet):
    queryset = FoodRating.objects.select_related('user', 'food__kitchen__chef').all()
    serializer_class = serializers.FoodRatingSerializer
    permission_classes = [IsUser]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['user']
    lookup_url_kwarg = 'review_id'
    http_method_names = ['get', 'post', 'patch']

    def get_queryset(self):
        food_id = self.kwargs.get('food_id', None)
        if food_id:
            return self.queryset.filter(food__id=food_id)
        return self.queryset
    

class KitchenReviewsView(generics.ListAPIView):
    queryset = FoodRating.objects.select_related('user', 'food__kitchen__chef').all()
    serializer_class = serializers.FoodRatingSerializer
    permission_classes = [IsUser]
    lookup_url_kwarg = "kitchen_id"

    def get_queryset(self):
        kitchen_id = self.kwargs[self.lookup_url_kwarg]
        return self.queryset.filter(food__kitchen__id=kitchen_id)


class ToggleLikeKitchenView(generics.UpdateAPIView):
    queryset = Kitchen.objects.all()
    serializer_class = serializers.ToggleLikeKitchenSerializer
    permission_classes = [IsUser]
    lookup_url_kwarg = "kitchen_id"
    http_method_names = ['put']


class ToggleLikeFoodView(generics.UpdateAPIView):
    queryset = Food.objects.all()
    serializer_class = serializers.ToggleLikeFoodSerializer
    permission_classes = [IsUser]
    lookup_url_kwarg = "food_id"
    http_method_names = ['put']




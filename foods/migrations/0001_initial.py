# Generated by Django 5.1.7 on 2025-05-30 13:12

import cloudinary_storage.storage
import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('user_profiles', '0002_chefuser_certified'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='KitchenAmenity',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Kitchen',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('cover_image', models.ImageField(upload_to='kitchen/cover_image/')),
                ('name', models.CharField(max_length=255, null=True)),
                ('type', models.CharField(choices=[('home_kitchen', 'Home Kitchen'), ('cloud_kitchen', 'Cloud Kitchen'), ('restaurant_kitchen', 'Restaurant Kitchen'), ('shared_kitchen', 'Shared Kitchen'), ('standard_kitchen', 'Standard Kitchen'), ('street_kitchen', 'Street Kitchen'), ('commissary_kitchen', 'Commissary Kitchen'), ('commercial_kitchen', 'Commercial Kitchen'), ('others', 'Others')], max_length=255)),
                ('others_type', models.CharField(default=None, max_length=50, null=True)),
                ('video', models.FileField(default=None, null=True, storage=cloudinary_storage.storage.VideoMediaCloudinaryStorage(), upload_to='kitchen/vett_videos/')),
                ('address', models.TextField(blank=True, null=True)),
                ('available', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('chef', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='kitchen', to='user_profiles.chefuser')),
                ('amenities', models.ManyToManyField(related_name='kitchens', to='foods.kitchenamenity')),
            ],
        ),
        migrations.CreateModel(
            name='Food',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('display_image', models.ImageField(upload_to='food/images/')),
                ('dietary', models.CharField(choices=[('keto', 'Keto'), ('vegan', 'Vegan'), ('low_fat', 'Low Fat'), ('high_protein', 'High Protein')], default=None, max_length=50, null=True)),
                ('cuisine', models.CharField(choices=[('nigeria', 'Nigeria'), ('inter_continental', 'Inter-Continental'), ('continental', 'Continental'), ('healthy', 'Healthy')], default=None, max_length=50, null=True)),
                ('preparation_type', models.CharField(choices=[('instant', 'Instant'), ('preorder', 'Pre-Order')], default='instant', max_length=50)),
                ('preparation_time_minutes', models.PositiveIntegerField(default=5)),
                ('breakfast', models.BooleanField(default=False)),
                ('lunch', models.BooleanField(default=False)),
                ('dinner', models.BooleanField(default=False)),
                ('drinks', models.BooleanField(default=False)),
                ('dessert_snacks', models.BooleanField(default=False)),
                ('name', models.CharField(max_length=150)),
                ('description', models.CharField(max_length=255)),
                ('price', models.DecimalField(decimal_places=2, default=None, max_digits=14, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))])),
                ('displayed', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('kitchen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='foods', to='foods.kitchen')),
            ],
        ),
        migrations.CreateModel(
            name='KitchenImageGroup',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('kitchen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='image_groups', to='foods.kitchen')),
            ],
        ),
        migrations.CreateModel(
            name='KitchenImage',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('image', models.ImageField(upload_to='kitchen/images/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='foods.kitchenimagegroup')),
            ],
        ),
        migrations.CreateModel(
            name='KitchenRating',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.PositiveSmallIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('kitchen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='foods.kitchen')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='kitchen_ratings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='FavouriteKitchen',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favourite_kitchen', to=settings.AUTH_USER_MODEL)),
                ('kitchen', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favourites', to='foods.kitchen')),
            ],
            options={
                'unique_together': {('user', 'kitchen')},
            },
        ),
    ]

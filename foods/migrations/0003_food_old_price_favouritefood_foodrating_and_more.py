# Generated by Django 5.1.7 on 2025-06-26 20:33

import django.core.validators
import django.db.models.deletion
import uuid
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('foods', '0002_kitchenrating_review'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='food',
            name='old_price',
            field=models.DecimalField(decimal_places=2, default=None, max_digits=14, null=True, validators=[django.core.validators.MinValueValidator(Decimal('0'))]),
        ),
        migrations.CreateModel(
            name='FavouriteFood',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('food', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favourites', to='foods.food')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favourite_food', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'food')},
            },
        ),
        migrations.CreateModel(
            name='FoodRating',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('rating', models.PositiveSmallIntegerField()),
                ('review', models.TextField(default=None, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('food', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='foods.food')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='food_ratings', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.DeleteModel(
            name='KitchenRating',
        ),
    ]

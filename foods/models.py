from django.db import models
from django.core.validators import MinValueValidator
from django.db.models import Avg
from rest_framework import exceptions
from authentication.models import User
from user_profiles.models import ChefUser
from decimal import Decimal
from cloudinary_storage.storage import RawMediaCloudinaryStorage, VideoMediaCloudinaryStorage
from uuid import uuid4


class KitchenAmenity(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    name = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self) -> str:
        return self.name


class Kitchen(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    chef = models.OneToOneField(ChefUser, related_name="kitchen", on_delete=models.CASCADE)
    cover_image = models.ImageField(upload_to='kitchen/cover_image/')
    name = models.Char<PERSON><PERSON>(max_length=255, null=True)
    class TYPE(models.TextChoices):
        HOME_KITCHEN=('home_kitchen', "Home Kitchen")
        CLOUD_KITCHEN=('cloud_kitchen', "Cloud Kitchen")
        RESTAURANT_KITCHEN=('restaurant_kitchen', "Restaurant Kitchen")
        SHARED_KITCHEN=('shared_kitchen', "Shared Kitchen")
        STANDARD_KITCHEN=('standard_kitchen', "Standard Kitchen")
        STREET_KITCHEN = ('street_kitchen', 'Street Kitchen')
        COMMISSARY_KITCHEN = ('commissary_kitchen', 'Commissary Kitchen')
        COMMERCIAL_KITCHEN = ('commercial_kitchen', 'Commercial Kitchen')
        OTHERS = ('others', 'Others')
    type = models.CharField(max_length=255, choices=TYPE.choices)
    others_type = models.CharField(max_length=50, null=True, default=None)
    amenities = models.ManyToManyField(KitchenAmenity, related_name='kitchens')
    video = models.FileField(upload_to='kitchen/vett_videos/', null=True, default=None, storage=VideoMediaCloudinaryStorage())#storage
    address = models.TextField(null=True, blank=True)
    available = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    @property
    def chef_vetted(self)->bool:
        return self.chef.vetted
    
    def is_favourite(self, user:User)->bool:
        return self.favourites.filter(user=user).exists()

    def __str__(self) -> str:
        return f"{self.chef}-{self.name}"

    def save(self, *args, **kwargs):
        if self.type == self.TYPE.OTHERS and not self.others_type:
            raise exceptions.ValidationError("provide the kitchen type not listed!")
        return super().save(*args, **kwargs)
    

class KitchenImageGroup(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    kitchen = models.ForeignKey(Kitchen, related_name="image_groups", on_delete=models.CASCADE)
    name = models.CharField(max_length=255, blank=True, null=True, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name or "Additional Images"


class KitchenImage(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    group = models.ForeignKey(KitchenImageGroup, related_name="images", on_delete=models.CASCADE)
    image = models.ImageField(upload_to='kitchen/images/')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return str(self.image)


class Food(models.Model):

    class DIETARY(models.TextChoices):
        KETO=('keto', "Keto")
        VEGAN=('vegan', "Vegan")
        LOW_FAT=('low_fat', "Low Fat")
        HIGH_PROTEIN=('high_protein', "High Protein")

    class CUISINE(models.TextChoices):
        NIGERIA=('nigeria', "Nigeria")
        INTER_CONTINENTAL=('inter_continental', "Inter-Continental")
        CONTINENTAL=('continental', "Continental")
        HEALTHY=('healthy', "Healthy")

    class PREPARATION(models.TextChoices):
        INSTANT = ('instant', 'Instant')
        PREORDER = ('preorder', 'Pre-Order')

    class STATUS(models.TextChoices):
        DRAFT = ('draft', 'Draft')
        UNDER_REVIEW = ('under_review', 'Under Review')
        APPROVED = ('approved', 'Approved')
        REJECTED = ('rejected', 'Rejected')

    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    kitchen = models.ForeignKey(Kitchen, related_name="foods", on_delete=models.CASCADE)
    display_image = models.ImageField(upload_to='food/images/')
    dietary = models.CharField(max_length=50, choices=DIETARY.choices, default=None, null=True)
    cuisine = models.CharField(max_length=50, choices=CUISINE.choices, default=None, null=True)
    preparation_type = models.CharField(max_length=50, choices=PREPARATION.choices, default=PREPARATION.INSTANT)
    preparation_time_minutes = models.PositiveIntegerField(default=5)
    
    breakfast = models.BooleanField(default=False)
    lunch = models.BooleanField(default=False)
    dinner = models.BooleanField(default=False)
    drinks = models.BooleanField(default=False)
    dessert_snacks = models.BooleanField(default=False)
    
    name = models.CharField(max_length=150)
    description = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=14, decimal_places=2, null=True, default=None, validators=[MinValueValidator(Decimal(0))])
    old_price = models.DecimalField(max_digits=14, decimal_places=2, null=True, default=None, validators=[MinValueValidator(Decimal(0))])
    displayed = models.BooleanField(default=True)

    status = models.CharField(max_length=50, choices=STATUS.choices, default=STATUS.DRAFT)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    @property
    def food_categories(self)->list[str]:
        categories = []
        if self.breakfast:
            categories.append('Breakfast')
        if self.lunch:
            categories.append('Lunch')
        if self.dinner:
            categories.append('Dinner')
        if self.drinks:
            categories.append('Drinks')
        if self.dessert_snacks:
            categories.append("Dessert & Drinks")
        return categories
    
    @property
    def chef_image(self)->str:
        return self.kitchen.chef.display_image.url

    @property
    def chef_vetted(self)->bool:
        return self.kitchen.chef.vetted
    
    @property
    def average_rating(self)->float:
        return self.ratings.aggregate(average=Avg('rating'))['average']
    
    def is_favourite(self, user:User)->bool:
        return self.favourites.filter(user=user).exists()

    def __str__(self) -> str:
        return self.name


class FavouriteKitchen(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, related_name="favourite_kitchen", on_delete=models.CASCADE)
    kitchen = models.ForeignKey(Kitchen, related_name="favourites", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'kitchen')

    def __str__(self) -> str:
        return f"{self.user}-{self.kitchen}"


class FavouriteFood(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, related_name="favourite_food", on_delete=models.CASCADE)
    food = models.ForeignKey(Food, related_name="favourites", on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'food')

    def __str__(self) -> str:
        return f"{self.user}-{self.food}"
    

class FoodRating(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid4, editable=False)
    user = models.ForeignKey(User, related_name="food_ratings", on_delete=models.CASCADE)
    food = models.ForeignKey(Food, related_name="ratings", on_delete=models.CASCADE)
    rating = models.PositiveSmallIntegerField()
    review = models.TextField(null=True, default=None)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self) -> str:
        return f"{self.food}-{self.rating}"


from django.core.management.base import BaseCommand
from django.db import transaction
from foods.models import KitchenAmenity

class Command(BaseCommand):
    help = 'Seeds amenities to the local database'

    def handle(self, *args, **kwargs):
        data = [
            'Wooden Spoons', 'Broom & Mop',
            "Spice Rack & Jars", 'Grater',
            'Microwave & Oven', 'Gas Cooker',
            'Motar & pestle', 'Cooking pot',
            'Refrigerator & Freezer'
            ]

        objs = [KitchenAmenity(name=name) for name in data]
        with transaction.atomic():
            # Refresh the database
            KitchenAmenity.objects.all().delete()
            KitchenAmenity.objects.bulk_create(objs)

        self.stdout.write(self.style.SUCCESS(f'{len(objs)} assets updated successfully!'))
       
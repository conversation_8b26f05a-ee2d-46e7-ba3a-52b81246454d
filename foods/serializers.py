from rest_framework import serializers
from .models import KitchenAmenity, Kitchen, KitchenImageGroup, KitchenImage, Food, FoodRating, FavouriteKitchen, FavouriteFood
from user_profiles.models import ChefUser
from drf_spectacular.utils import extend_schema_field


class GetChefProfileFoodSerializer(serializers.ModelSerializer):
    first_name = serializers.CharField(source='user__first_name')
    last_name = serializers.CharField(source='user__last_name')
    services = serializers.SerializerMethodField()

    def get_services(self, obj:ChefUser)->list[str]:
        _services = [i.lower() for i in obj.special_service.values_list('name', flat=True)]
        if 'others' in _services:
            _services.remove('others')
            _services.append(obj.other_special_services)
        return _services

    class Meta:
        model = ChefUser
        fields = ['id', 'display_image', 'first_name', 'last_name', 'vetted', 'certified', 'years_cooking', 'school_attended', 'services' ]


class GetMarketPlaceFoodSerializer(serializers.ModelSerializer):

    class Meta:
        model = Food
        fields = ['id', 'name', 'display_image', 'dietary', 'preparation_time_minutes', 'price', 'old_price', 'average_rating']


class GetUserMarketPlaceKitchenSerializer(serializers.ModelSerializer):
    liked = serializers.SerializerMethodField()
    chef = GetChefProfileFoodSerializer(read_only=True)
    foods = serializers.SerializerMethodField()

    def get_liked(self, obj:Kitchen)->bool:
        return obj.is_favourite(self.context['request'].user)
    
    @extend_schema_field(GetMarketPlaceFoodSerializer(many=True))
    def get_foods(self, kitchen: Kitchen):
        qs = kitchen.foods.all()

        params = self.context.get('food_filters', {})

        if params.get('breakfast'):
            qs = qs.filter(breakfast=True)
        if params.get('lunch'):
            qs = qs.filter(lunch=True)
        if params.get('dinner'):
            qs = qs.filter(dinner=True)
        if params.get('drinks'):
            qs = qs.filter(drinks=True)
        if params.get('dessert_snacks'):
            qs = qs.filter(dessert_snacks=True)

        if cuisine := params.get('cuisine'):
            qs = qs.filter(cuisine__iexact=cuisine)

        if prep := params.get('preparation_type'):
            qs = qs.filter(preparation_type__iexact=prep)

        if diet := params.get('dietary'):
            qs = qs.filter(dietary__iexact=diet)

        if price_min := params.get('price_min'):
            qs = qs.filter(price__gte=price_min)

        if price_max := params.get('price_max'):
            qs = qs.filter(price__lte=price_max)

        if search := params.get('search'):
            qs = qs.filter(name__icontains=search)

        return GetMarketPlaceFoodSerializer(qs, many=True, context=self.context).data
    
    class Meta:
        model = Kitchen
        fields = ['id', 'liked', 'chef', 'available', 'foods',]


class GetChefKitchenSerializer(serializers.ModelSerializer):
    type = serializers.SerializerMethodField()
    chef = GetChefProfileFoodSerializer(read_only=True)
    liked = serializers.SerializerMethodField()

    def get_type(self, obj:Kitchen)->str:
        if obj.type == obj.TYPE.OTHERS and obj.others_type:
            return obj.others_type
        return obj.type

    def get_liked(self, obj:Kitchen)->bool:
        return obj.is_favourite(self.context['request'].user)

    class Meta:
        model = Kitchen
        fields = ['id', 'name', 'type', 'liked', 'chef', 'cover_image', 'available']


class GetChefKitchenAmenitiesListSerializer(serializers.ModelSerializer):
    class Meta:
        model = KitchenAmenity
        fields = ['id', 'name']


class FoodRatingSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    display_image = serializers.ImageField(source='food.kitchen.chef.display_image', read_only=True)

    def get_full_name(self, obj:FoodRating)->str:
        return obj.user.get_full_name()

    class Meta:
        model = FoodRating
        fields = ['id', 'full_name', 'display_image', 'rating', 'review', 'created_at']
        read_only_fields = ['id', 'created_at']

    def create(self, validated_data):
        validated_data['user'] = self.context['request'].user
        validated_data['food_id'] = self.context['view'].kwargs.get('food_id')
        return super().create(validated_data)


class GetKitchenImagesSerializer(serializers.ModelSerializer):
    class Meta:
        model = KitchenImage
        fields = ['image']


class GetKitchenImageGroupSerializer(serializers.ModelSerializer):
    images = GetKitchenImagesSerializer(many=True)
    class Meta:
        model = KitchenImageGroup
        fields = ['name', 'images']


class ToggleLikeKitchenSerializer(serializers.ModelSerializer):
    like = serializers.BooleanField(write_only=True)
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj:Kitchen)->str:
        return f"kitchen {'liked' if self.validated_data['like'] else 'unliked'} successfully!"
    
    class Meta:
        model = Kitchen
        fields = ['like', 'msg']


    def update(self, instance:Kitchen, validated_data):
        if validated_data['like']:
            user = self.context['request'].user
            if not FavouriteKitchen.objects.filter(user=user, kitchen=instance).exists():
                FavouriteKitchen.objects.create(user=user, kitchen=instance)
        else:
            FavouriteKitchen.objects.filter(user=user, kitchen=instance).delete()
        return instance
    

class ToggleLikeFoodSerializer(serializers.ModelSerializer):
    like = serializers.BooleanField(write_only=True)
    msg = serializers.SerializerMethodField()

    def get_msg(self, obj:Kitchen)->str:
        return f"Food {'liked' if self.validated_data['like'] else 'unliked'} successfully!"
    
    class Meta:
        model = Food
        fields = ['like', 'msg']


    def update(self, instance:Food, validated_data):
        if validated_data['like']:
            user = self.context['request'].user
            if not FavouriteFood.objects.filter(user=user, food=instance).exists():
                FavouriteFood.objects.create(user=user, food=instance)
        else:
            FavouriteFood.objects.filter(user=user, food=instance).delete()
        return instance
    

class RetrieveFoodWithListSerializer(GetMarketPlaceFoodSerializer):
    class Meta:
        model = Food
        fields = ['id', 'name', 'description', 'display_image', 'dietary', 'preparation_time_minutes', 'price', 'old_price', 'food_categories']



from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

review_router = DefaultRouter()
review_router.register(r'reviews', views.ReviewRatingViewSet, basename='kitchen-reviews')


urlpatterns = [
    path('dashboard/marketplace/', views.GetMarketPlaceFoodView.as_view(), name='market-place-list'),
    path('dashboard/chef-kitchen/', views.GetChefKitchenFoodView.as_view(), name='chefs-kitchen-list'),
    path('food-list/<uuid:kitchen_id>/', views.FoodListView.as_view(), name='get-food-list'),
    path('amenities-list/<uuid:kitchen_id>/', views.GetKitchenAmenitiesListView.as_view(), name='kitchen-amenities-list'),
    path('kitchen-images-list/<uuid:kitchen_id>/', views.KitchenImageListView.as_view(), name='kitchen-images'),
    path('chef-reviews/<uuid:kitchen_id>/', views.KitchenReviewsView.as_view(), name='kitchen-reviews'),
    path('<uuid:food_id>/', include(review_router.urls), name='reviews-rating'),
    path('kitchen/toggle-like/<uuid:kitchen_id>/', views.ToggleLikeKitchenView.as_view(), name='kitchen-like-unlike'),
    path('food/toggle-like/<uuid:food_id>/', views.ToggleLikeFoodView.as_view(), name='food-like-unlike'),
]
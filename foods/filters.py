import django_filters
from django.contrib.postgres.search import SearchVector, SearchQuery, TrigramSimilarity
from django.db.models import Count, Q, Case, When, IntegerField
from .models import Kitchen, Food


class FuzzySearchFilter(django_filters.CharFilter):
    def filter(self, qs, value):
        if not value:
            return qs

        value = value.strip()

        return qs.annotate(
            kitchen_similarity=TrigramSimilarity('name', value)
        ).filter(
            Q(name__icontains=value) | 
            Q(kitchen_similarity__gt=0.3) |
            Q(food__name__icontains=value) 
        ).distinct().order_by('-kitchen_similarity')
    

class MarketPlaceFilter(django_filters.FilterSet):
    breakfast = django_filters.BooleanFilter(field_name='food__breakfast')
    lunch = django_filters.BooleanFilter(field_name='food__lunch')
    dinner = django_filters.BooleanFilter(field_name='food__dinner')
    drinks = django_filters.BooleanFilter(field_name='food__drinks')
    dessert_snacks = django_filters.BooleanFilter(field_name='food__dessert_snacks')

    preparation_type = django_filters.CharFilter(field_name='food__preparation_type', lookup_expr='iexact')
    cuisine = django_filters.CharFilter(field_name='food__cuisine', lookup_expr='iexact')
    dietary = django_filters.CharFilter(field_name='food__dietary', lookup_expr='iexact')

    price_min = django_filters.NumberFilter(field_name='food__price', lookup_expr='gte')
    price_max = django_filters.NumberFilter(field_name='food__price', lookup_expr='lte')

    search = FuzzySearchFilter()

    class Meta:
        model = Kitchen
        fields = [
            'breakfast', 'lunch', 'dinner', 'drinks', 'dessert_snacks',
            'preparation_type', 'cuisine', 'dietary', 'price_min', 'price_max', 'search'
        ]



class FoodSearchFilter(django_filters.CharFilter):
    '''FTS + Trigram search'''
    def filter(self, qs, value):
        if not value:
            return qs

        fts_query = SearchQuery(value)

        return qs.annotate(
            search=SearchVector('name', 'description'),
            similarity=TrigramSimilarity('name', value) + TrigramSimilarity('description', value)
        ).filter(
            Q(search=fts_query) | Q(similarity__gt=0.3)
        ).order_by('-similarity')


class FoodListFilter(django_filters.FilterSet):
    price_min = django_filters.NumberFilter(field_name='price', lookup_expr='gte')
    price_max = django_filters.NumberFilter(field_name='price', lookup_expr='lte')
    top_food_id = django_filters.UUIDFilter(field_name='id', method='filter_top_food')
    search = FoodSearchFilter()

    class Meta:
        model = Food
        fields = ['breakfast', 'lunch', 'dinner', 'drinks', 'dessert_snacks', 'preparation_type', 'cuisine', 'dietary', 'price_min', 'price_max', 'search', 'top_food_id']

    def filter_top_food(self, queryset, name, value):
        if not value:
            return queryset

        # Annotate with a pinned field to prioritize the top food
        return queryset.annotate(
            pinned=Case(When(id=value, then=0), default=1, output_field=IntegerField())
        ).order_by('-pinned', '-created_at')

